{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspace/novo/src/components/FloatingCashbackCards.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\n\nconst floatingCards = [\n  {\n    id: 1,\n    store: 'Amazon',\n    logo: '/logos/amazon.svg',\n    rate: '5.5%',\n    platform: 'Rakuten',\n    position: { top: '15%', left: '10%' },\n    delay: 0,\n    color: '#FF9900',\n  },\n  {\n    id: 2,\n    store: 'Nike',\n    logo: '/logos/nike.svg',\n    rate: '8.0%',\n    platform: 'Honey',\n    position: { top: '25%', right: '15%' },\n    delay: 0.5,\n    color: '#000000',\n  },\n  {\n    id: 3,\n    store: 'Target',\n    logo: '/logos/target.svg',\n    rate: '3.5%',\n    platform: 'TopCashback',\n    position: { bottom: '30%', left: '8%' },\n    delay: 1,\n    color: '#CC0000',\n  },\n  {\n    id: 4,\n    store: 'Best Buy',\n    logo: '/logos/bestbuy.svg',\n    rate: '4.2%',\n    platform: 'Cashback Monitor',\n    position: { bottom: '20%', right: '12%' },\n    delay: 1.5,\n    color: '#0046BE',\n  },\n  {\n    id: 5,\n    store: 'Walmart',\n    logo: '/logos/walmart.svg',\n    rate: '2.8%',\n    platform: 'BeFrugal',\n    position: { top: '45%', left: '5%' },\n    delay: 2,\n    color: '#004C91',\n  },\n  {\n    id: 6,\n    store: 'Apple',\n    logo: '/logos/apple.svg',\n    rate: '1.5%',\n    platform: 'Rakuten',\n    position: { top: '35%', right: '8%' },\n    delay: 2.5,\n    color: '#000000',\n  },\n]\n\nexport default function FloatingCashbackCards() {\n  return (\n    <div className=\"pointer-events-none absolute inset-0 overflow-hidden\">\n      {floatingCards.map(card => (\n        <motion.div\n          key={card.id}\n          initial={{ opacity: 0, scale: 0.8, y: 50 }}\n          animate={{\n            opacity: [0, 1, 1, 0.7],\n            scale: [0.8, 1, 1, 0.9],\n            y: [50, 0, -10, 0],\n          }}\n          transition={{\n            duration: 4,\n            delay: card.delay,\n            repeat: Infinity,\n            repeatType: 'reverse',\n            ease: 'easeInOut',\n          }}\n          className=\"absolute hidden lg:block\"\n          style={card.position}\n        >\n          <div className=\"card-gradient shadow-green-soft min-w-[200px] rounded-2xl border border-white/20 p-4 backdrop-blur-sm\">\n            {/* Store Header */}\n            <div className=\"mb-3 flex items-center space-x-3\">\n              <div\n                className=\"flex h-10 w-10 items-center justify-center rounded-xl shadow-sm\"\n                style={{ backgroundColor: `${card.color}15` }}\n              >\n                {/* Store logo representation */}\n                <div\n                  className=\"flex h-6 w-6 items-center justify-center rounded text-xs font-bold text-white\"\n                  style={{ backgroundColor: card.color }}\n                >\n                  {card.store.charAt(0)}\n                </div>\n              </div>\n              <div>\n                <h3 className=\"text-sm font-semibold text-gray-900\">{card.store}</h3>\n                <p className=\"text-xs text-gray-500\">via {card.platform}</p>\n              </div>\n            </div>\n\n            {/* Cashback Rate */}\n            <div className=\"text-center\">\n              <div className=\"mb-1 font-poppins text-2xl font-bold text-emerald-600\">\n                {card.rate}\n              </div>\n              <div className=\"text-xs font-medium text-gray-500\">Cashback Rate</div>\n            </div>\n\n            {/* Trend Indicator */}\n            <div className=\"mt-2 flex items-center justify-center\">\n              <motion.div\n                animate={{ scale: [1, 1.1, 1] }}\n                transition={{ duration: 2, repeat: Infinity }}\n                className=\"flex items-center space-x-1 text-xs text-emerald-600\"\n              >\n                <svg className=\"h-3 w-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path\n                    fillRule=\"evenodd\"\n                    d=\"M5.293 7.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L6.707 7.707a1 1 0 01-1.414 0z\"\n                    clipRule=\"evenodd\"\n                  />\n                </svg>\n                <span className=\"font-medium\">Trending</span>\n              </motion.div>\n            </div>\n          </div>\n        </motion.div>\n      ))}\n\n      {/* Mobile Floating Elements - Simplified */}\n      <div className=\"lg:hidden\">\n        {[1, 2, 3].map(index => (\n          <motion.div\n            key={`mobile-${index}`}\n            initial={{ opacity: 0, scale: 0.5 }}\n            animate={{\n              opacity: [0, 0.6, 0.6, 0.3],\n              scale: [0.5, 0.8, 0.8, 0.6],\n              y: [30, 0, -5, 0],\n            }}\n            transition={{\n              duration: 3,\n              delay: index * 0.5,\n              repeat: Infinity,\n              repeatType: 'reverse',\n              ease: 'easeInOut',\n            }}\n            className=\"absolute\"\n            style={{\n              top: `${20 + index * 25}%`,\n              [index % 2 === 0 ? 'left' : 'right']: '5%',\n            }}\n          >\n            <div className=\"glass-green shadow-green-soft rounded-xl p-3\">\n              <div className=\"text-lg font-bold text-emerald-600\">\n                {index === 1 ? '5.5%' : index === 2 ? '8.0%' : '3.5%'}\n              </div>\n              <div className=\"text-xs text-emerald-700\">Cashback</div>\n            </div>\n          </motion.div>\n        ))}\n      </div>\n\n      {/* Animated Background Particles */}\n      <div className=\"absolute inset-0\">\n        {[...Array(20)].map((_, index) => (\n          <motion.div\n            key={`particle-${index}`}\n            initial={{\n              opacity: 0,\n              x: Math.random() * 1200,\n              y: Math.random() * 800,\n            }}\n            animate={{\n              opacity: [0, 0.3, 0],\n              x: Math.random() * 1200,\n              y: Math.random() * 800,\n            }}\n            transition={{\n              duration: Math.random() * 10 + 10,\n              repeat: Infinity,\n              repeatType: 'reverse',\n              ease: 'linear',\n              delay: Math.random() * 5,\n            }}\n            className=\"absolute h-1 w-1 rounded-full bg-white\"\n          />\n        ))}\n      </div>\n\n      {/* Dollar Sign Animations */}\n      {[1, 2, 3, 4, 5].map(index => (\n        <motion.div\n          key={`dollar-${index}`}\n          initial={{\n            opacity: 0,\n            y: 100,\n            x: Math.random() * 1200,\n          }}\n          animate={{\n            opacity: [0, 0.4, 0],\n            y: -100,\n          }}\n          transition={{\n            duration: 8,\n            repeat: Infinity,\n            delay: index * 2,\n            ease: 'linear',\n          }}\n          className=\"pointer-events-none absolute text-2xl font-bold text-white/20\"\n        >\n          $\n        </motion.div>\n      ))}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,MAAM,gBAAgB;IACpB;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,UAAU;QACV,UAAU;YAAE,KAAK;YAAO,MAAM;QAAM;QACpC,OAAO;QACP,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,UAAU;QACV,UAAU;YAAE,KAAK;YAAO,OAAO;QAAM;QACrC,OAAO;QACP,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,UAAU;QACV,UAAU;YAAE,QAAQ;YAAO,MAAM;QAAK;QACtC,OAAO;QACP,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,UAAU;QACV,UAAU;YAAE,QAAQ;YAAO,OAAO;QAAM;QACxC,OAAO;QACP,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,UAAU;QACV,UAAU;YAAE,KAAK;YAAO,MAAM;QAAK;QACnC,OAAO;QACP,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,UAAU;QACV,UAAU;YAAE,KAAK;YAAO,OAAO;QAAK;QACpC,OAAO;QACP,OAAO;IACT;CACD;AAEc,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;;YACZ,cAAc,GAAG,CAAC,CAAA,qBACjB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,SAAS;wBAAE,SAAS;wBAAG,OAAO;wBAAK,GAAG;oBAAG;oBACzC,SAAS;wBACP,SAAS;4BAAC;4BAAG;4BAAG;4BAAG;yBAAI;wBACvB,OAAO;4BAAC;4BAAK;4BAAG;4BAAG;yBAAI;wBACvB,GAAG;4BAAC;4BAAI;4BAAG,CAAC;4BAAI;yBAAE;oBACpB;oBACA,YAAY;wBACV,UAAU;wBACV,OAAO,KAAK,KAAK;wBACjB,QAAQ;wBACR,YAAY;wBACZ,MAAM;oBACR;oBACA,WAAU;oBACV,OAAO,KAAK,QAAQ;8BAEpB,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,iBAAiB,GAAG,KAAK,KAAK,CAAC,EAAE,CAAC;wCAAC;kDAG5C,cAAA,6LAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,iBAAiB,KAAK,KAAK;4CAAC;sDAEpC,KAAK,KAAK,CAAC,MAAM,CAAC;;;;;;;;;;;kDAGvB,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAuC,KAAK,KAAK;;;;;;0DAC/D,6LAAC;gDAAE,WAAU;;oDAAwB;oDAAK,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;0CAK3D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACZ,KAAK,IAAI;;;;;;kDAEZ,6LAAC;wCAAI,WAAU;kDAAoC;;;;;;;;;;;;0CAIrD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,OAAO;4CAAC;4CAAG;4CAAK;yCAAE;oCAAC;oCAC9B,YAAY;wCAAE,UAAU;wCAAG,QAAQ;oCAAS;oCAC5C,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAe,SAAQ;sDACnD,cAAA,6LAAC;gDACC,UAAS;gDACT,GAAE;gDACF,UAAS;;;;;;;;;;;sDAGb,6LAAC;4CAAK,WAAU;sDAAc;;;;;;;;;;;;;;;;;;;;;;;mBA5D/B,KAAK,EAAE;;;;;0BAoEhB,6LAAC;gBAAI,WAAU;0BACZ;oBAAC;oBAAG;oBAAG;iBAAE,CAAC,GAAG,CAAC,CAAA,sBACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAI;wBAClC,SAAS;4BACP,SAAS;gCAAC;gCAAG;gCAAK;gCAAK;6BAAI;4BAC3B,OAAO;gCAAC;gCAAK;gCAAK;gCAAK;6BAAI;4BAC3B,GAAG;gCAAC;gCAAI;gCAAG,CAAC;gCAAG;6BAAE;wBACnB;wBACA,YAAY;4BACV,UAAU;4BACV,OAAO,QAAQ;4BACf,QAAQ;4BACR,YAAY;4BACZ,MAAM;wBACR;wBACA,WAAU;wBACV,OAAO;4BACL,KAAK,GAAG,KAAK,QAAQ,GAAG,CAAC,CAAC;4BAC1B,CAAC,QAAQ,MAAM,IAAI,SAAS,QAAQ,EAAE;wBACxC;kCAEA,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACZ,UAAU,IAAI,SAAS,UAAU,IAAI,SAAS;;;;;;8CAEjD,6LAAC;oCAAI,WAAU;8CAA2B;;;;;;;;;;;;uBAxBvC,CAAC,OAAO,EAAE,OAAO;;;;;;;;;;0BA+B5B,6LAAC;gBAAI,WAAU;0BACZ;uBAAI,MAAM;iBAAI,CAAC,GAAG,CAAC,CAAC,GAAG,sBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BACP,SAAS;4BACT,GAAG,KAAK,MAAM,KAAK;4BACnB,GAAG,KAAK,MAAM,KAAK;wBACrB;wBACA,SAAS;4BACP,SAAS;gCAAC;gCAAG;gCAAK;6BAAE;4BACpB,GAAG,KAAK,MAAM,KAAK;4BACnB,GAAG,KAAK,MAAM,KAAK;wBACrB;wBACA,YAAY;4BACV,UAAU,KAAK,MAAM,KAAK,KAAK;4BAC/B,QAAQ;4BACR,YAAY;4BACZ,MAAM;4BACN,OAAO,KAAK,MAAM,KAAK;wBACzB;wBACA,WAAU;uBAlBL,CAAC,SAAS,EAAE,OAAO;;;;;;;;;;YAwB7B;gBAAC;gBAAG;gBAAG;gBAAG;gBAAG;aAAE,CAAC,GAAG,CAAC,CAAA,sBACnB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,SAAS;wBACP,SAAS;wBACT,GAAG;wBACH,GAAG,KAAK,MAAM,KAAK;oBACrB;oBACA,SAAS;wBACP,SAAS;4BAAC;4BAAG;4BAAK;yBAAE;wBACpB,GAAG,CAAC;oBACN;oBACA,YAAY;wBACV,UAAU;wBACV,QAAQ;wBACR,OAAO,QAAQ;wBACf,MAAM;oBACR;oBACA,WAAU;8BACX;mBAjBM,CAAC,OAAO,EAAE,OAAO;;;;;;;;;;;AAuBhC;KA/JwB", "debugId": null}}, {"offset": {"line": 450, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspace/novo/src/components/HeroSection.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { MagnifyingGlassIcon, SparklesIcon, ShieldCheckIcon } from '@heroicons/react/24/outline'\nimport { motion } from 'framer-motion'\nimport FloatingCashbackCards from './FloatingCashbackCards'\n\nconst trustStats = [\n  { value: '2M+', label: 'Usuários Ativos', icon: SparklesIcon },\n  { value: '500+', label: 'Lojas Parceiras', icon: ShieldCheckIcon },\n  { value: 'R$50M+', label: 'Cashback Ganho', icon: SparklesIcon },\n]\n\nexport default function HeroSection() {\n  const [searchQuery, setSearchQuery] = useState('')\n\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault()\n    // Handle search logic here\n    console.log('Searching for:', searchQuery)\n  }\n\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden pt-20\">\n      {/* Background Gradient */}\n      <div className=\"absolute inset-0 hero-gradient\" />\n      \n      {/* Geometric Background Elements */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute top-20 left-10 w-32 h-32 bg-white rounded-full blur-xl\" />\n        <div className=\"absolute top-40 right-20 w-24 h-24 bg-white rounded-full blur-lg\" />\n        <div className=\"absolute bottom-40 left-20 w-40 h-40 bg-white rounded-full blur-2xl\" />\n        <div className=\"absolute bottom-20 right-10 w-28 h-28 bg-white rounded-full blur-xl\" />\n      </div>\n\n      {/* Floating Cashback Cards */}\n      <FloatingCashbackCards />\n\n      {/* Main Content */}\n      <div className=\"relative z-10 container-responsive text-center\">\n        <div className=\"max-w-4xl mx-auto\">\n          {/* Badge */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            className=\"inline-flex items-center px-4 py-2 rounded-full glass-green text-emerald-700 font-medium text-sm mb-8\"\n          >\n            <SparklesIcon className=\"w-4 h-4 mr-2\" />\n            Compare. Economize. Ganhe Mais.\n          </motion.div>\n\n          {/* Main Headline */}\n          <motion.h1\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            className=\"text-responsive-3xl font-poppins font-black text-white mb-6 leading-tight\"\n          >\n            Encontre as{' '}\n            <span className=\"relative\">\n              <span className=\"bg-gradient-to-r from-yellow-300 via-yellow-200 to-yellow-100 bg-clip-text text-transparent\">\n                Melhores Taxas de Cashback\n              </span>\n              <motion.div\n                className=\"absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-yellow-300 to-yellow-100 rounded-full\"\n                initial={{ scaleX: 0 }}\n                animate={{ scaleX: 1 }}\n                transition={{ duration: 0.8, delay: 1 }}\n              />\n            </span>\n            {' '}em Todas as Plataformas\n          </motion.h1>\n\n          {/* Subtitle */}\n          <motion.p\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.4 }}\n            className=\"text-responsive-lg text-emerald-50 mb-12 max-w-2xl mx-auto leading-relaxed\"\n          >\n            Compare taxas de cashback ao vivo instantaneamente. Sem cadastro necessário. Veja qual plataforma oferece as melhores taxas para suas lojas favoritas agora mesmo.\n          </motion.p>\n\n          {/* Search Bar */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.6 }}\n            className=\"max-w-2xl mx-auto mb-12\"\n          >\n            <form onSubmit={handleSearch} className=\"relative\">\n              <div className=\"relative\">\n                <MagnifyingGlassIcon className=\"absolute left-4 top-1/2 transform -translate-y-1/2 w-6 h-6 text-gray-400\" />\n                <input\n                  type=\"text\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                  placeholder=\"Busque por lojas como Amazon, Nike, Magazine Luiza...\"\n                  className=\"w-full pl-12 pr-40 py-4 text-lg rounded-2xl border-0 shadow-green-strong focus:ring-4 focus:ring-white/20 focus:shadow-green-strong bg-white/95 backdrop-blur-sm placeholder-gray-500 text-gray-900 outline-none transition-all duration-300\"\n                />\n                <button\n                  type=\"submit\"\n                  className=\"absolute right-3 top-1/2 transform -translate-y-1/2 bg-emerald-500 hover:bg-emerald-600 text-white font-semibold px-6 py-2.5 rounded-xl transition-all duration-200 shadow-md hover:shadow-lg\"\n                >\n                  Comparar Taxas\n                </button>\n              </div>\n            </form>\n            \n            {/* Popular Searches */}\n            <div className=\"mt-6 bg-white/10 backdrop-blur-sm rounded-2xl p-4 border border-white/20\">\n              <div className=\"flex flex-wrap justify-center items-center gap-3\">\n                <span className=\"text-white font-medium text-sm\">Lojas populares:</span>\n                {['Amazon', 'Nike', 'Magazine Luiza', 'Americanas', 'Casas Bahia'].map((store) => (\n                  <button\n                    key={store}\n                    onClick={() => setSearchQuery(store)}\n                    className=\"px-4 py-2 text-sm text-white bg-white/20 hover:bg-white/30 border border-white/30 hover:border-white/50 rounded-full transition-all duration-200 focus-visible font-medium\"\n                  >\n                    {store}\n                  </button>\n                ))}\n              </div>\n            </div>\n          </motion.div>\n\n          {/* Trust Stats */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.8 }}\n            className=\"grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto\"\n          >\n            {trustStats.map((stat, index) => (\n              <motion.div\n                key={stat.label}\n                initial={{ opacity: 0, scale: 0.8 }}\n                animate={{ opacity: 1, scale: 1 }}\n                transition={{ duration: 0.6, delay: 1 + index * 0.1 }}\n                className=\"bg-white/95 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 text-center\"\n              >\n                <div className=\"inline-flex items-center justify-center w-12 h-12 rounded-xl bg-emerald-100 mb-4\">\n                  <stat.icon className=\"w-6 h-6 text-emerald-600\" />\n                </div>\n                <div className=\"text-2xl md:text-3xl font-poppins font-bold text-gray-900 mb-2\">\n                  {stat.value}\n                </div>\n                <div className=\"text-gray-600 text-sm font-medium\">\n                  {stat.label}\n                </div>\n              </motion.div>\n            ))}\n          </motion.div>\n\n          {/* CTA Buttons */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 1.2 }}\n            className=\"flex flex-col sm:flex-row gap-4 justify-center mt-12\"\n          >\n            <button className=\"btn-primary text-lg px-8 py-4 animate-pulse-green\">\n              Ver Taxas ao Vivo Abaixo ↓\n            </button>\n            <button className=\"btn-secondary bg-white/10 text-white border-white/20 hover:bg-white/20 text-lg px-8 py-4\">\n              Comparar Todas as Plataformas\n            </button>\n          </motion.div>\n        </div>\n      </div>\n\n      {/* Scroll Indicator */}\n      <motion.div\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        transition={{ duration: 1, delay: 1.5 }}\n        className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\n      >\n        <motion.div\n          animate={{ y: [0, 10, 0] }}\n          transition={{ duration: 2, repeat: Infinity }}\n          className=\"w-6 h-10 border-2 border-white/30 rounded-full flex justify-center\"\n        >\n          <motion.div\n            animate={{ y: [0, 12, 0] }}\n            transition={{ duration: 2, repeat: Infinity }}\n            className=\"w-1 h-3 bg-white/60 rounded-full mt-2\"\n          />\n        </motion.div>\n      </motion.div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AACA;;;AALA;;;;;AAOA,MAAM,aAAa;IACjB;QAAE,OAAO;QAAO,OAAO;QAAmB,MAAM,0NAAA,CAAA,eAAY;IAAC;IAC7D;QAAE,OAAO;QAAQ,OAAO;QAAmB,MAAM,gOAAA,CAAA,kBAAe;IAAC;IACjE;QAAE,OAAO;QAAU,OAAO;QAAkB,MAAM,0NAAA,CAAA,eAAY;IAAC;CAChE;AAEc,SAAS;;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,2BAA2B;QAC3B,QAAQ,GAAG,CAAC,kBAAkB;IAChC;IAEA,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,6LAAC,8IAAA,CAAA,UAAqB;;;;;0BAGtB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;;8CAEV,6LAAC,0NAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAK3C,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;gCACX;gCACa;8CACZ,6LAAC;oCAAK,WAAU;;sDACd,6LAAC;4CAAK,WAAU;sDAA8F;;;;;;sDAG9G,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,QAAQ;4CAAE;4CACrB,SAAS;gDAAE,QAAQ;4CAAE;4CACrB,YAAY;gDAAE,UAAU;gDAAK,OAAO;4CAAE;;;;;;;;;;;;gCAGzC;gCAAI;;;;;;;sCAIP,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;sCACX;;;;;;sCAKD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,6LAAC;oCAAK,UAAU;oCAAc,WAAU;8CACtC,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,wOAAA,CAAA,sBAAmB;gDAAC,WAAU;;;;;;0DAC/B,6LAAC;gDACC,MAAK;gDACL,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,aAAY;gDACZ,WAAU;;;;;;0DAEZ,6LAAC;gDACC,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;;8CAOL,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAiC;;;;;;4CAChD;gDAAC;gDAAU;gDAAQ;gDAAkB;gDAAc;6CAAc,CAAC,GAAG,CAAC,CAAC,sBACtE,6LAAC;oDAEC,SAAS,IAAM,eAAe;oDAC9B,WAAU;8DAET;mDAJI;;;;;;;;;;;;;;;;;;;;;;sCAYf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;sCAET,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO,IAAI,QAAQ;oCAAI;oCACpD,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;;;;;;sDAEvB,6LAAC;4CAAI,WAAU;sDACZ,KAAK,KAAK;;;;;;sDAEb,6LAAC;4CAAI,WAAU;sDACZ,KAAK,KAAK;;;;;;;mCAbR,KAAK,KAAK;;;;;;;;;;sCAoBrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,6LAAC;oCAAO,WAAU;8CAAoD;;;;;;8CAGtE,6LAAC;oCAAO,WAAU;8CAA2F;;;;;;;;;;;;;;;;;;;;;;;0BAQnH,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,UAAU;oBAAG,OAAO;gBAAI;gBACtC,WAAU;0BAEV,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,GAAG;4BAAC;4BAAG;4BAAI;yBAAE;oBAAC;oBACzB,YAAY;wBAAE,UAAU;wBAAG,QAAQ;oBAAS;oBAC5C,WAAU;8BAEV,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,GAAG;gCAAC;gCAAG;gCAAI;6BAAE;wBAAC;wBACzB,YAAY;4BAAE,UAAU;4BAAG,QAAQ;wBAAS;wBAC5C,WAAU;;;;;;;;;;;;;;;;;;;;;;AAMtB;GApLwB;KAAA", "debugId": null}}, {"offset": {"line": 951, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspace/novo/src/components/HowItWorksSection.tsx"], "sourcesContent": ["'use client'\n\nimport {\n  ChartBarIcon,\n  CurrencyDollarIcon,\n  MagnifyingGlassIcon,\n  SparklesIcon,\n} from '@heroicons/react/24/outline'\nimport { motion } from 'framer-motion'\n\nconst steps = [\n  {\n    id: 1,\n    title: 'Busque e Compare',\n    description:\n      'Digite o nome de qualquer loja ou navegue por categoria para ver taxas de cashback em tempo real de todas as principais plataformas.',\n    icon: MagnifyingGlassIcon,\n    color: 'from-blue-500 to-cyan-500',\n    features: ['500+ lojas', 'Taxas em tempo real', 'Todas as plataformas'],\n  },\n  {\n    id: 2,\n    title: 'Encontre as Melhores Taxas',\n    description:\n      'Nosso mecanismo de comparação inteligente mostra instantaneamente qual plataforma oferece o maior cashback para sua compra.',\n    icon: ChartBarIcon,\n    color: 'from-emerald-500 to-green-500',\n    features: ['Comparação inteligente', 'Análise de tendências', 'Alertas de taxa'],\n  },\n  {\n    id: 3,\n    title: 'Comece a Ganhar',\n    description:\n      'Clique na plataforma escolhida e comece a ganhar o máximo de cashback em cada compra que fizer.',\n    icon: CurrencyDollarIcon,\n    color: 'from-yellow-500 to-orange-500',\n    features: ['Ganhos máximos', 'Rastreamento instantâneo', 'Pagamentos fáceis'],\n  },\n]\n\nconst benefits = [\n  'Economize tempo comparando taxas manualmente',\n  'Nunca perca as melhores ofertas de cashback',\n  'Maximize os ganhos em cada compra',\n  'Mantenha-se atualizado com mudanças de taxa',\n  'Acesse bônus exclusivos das plataformas',\n  'Acompanhe suas economias totais',\n]\n\nexport default function HowItWorksSection() {\n  return (\n    <section\n      id=\"how-it-works\"\n      className=\"py-responsive bg-gradient-to-br from-emerald-50 via-green-50 to-teal-50\"\n    >\n      <div className=\"container-responsive\">\n        {/* Section Header */}\n        <div className=\"mb-16 text-center\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"mb-6 inline-flex items-center rounded-full bg-white/80 px-4 py-2 text-sm font-medium text-emerald-700 shadow-sm backdrop-blur-sm\"\n          >\n            <SparklesIcon className=\"mr-2 h-4 w-4\" />\n            Processo Simples de 3 Passos\n          </motion.div>\n\n          <motion.h2\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.1 }}\n            viewport={{ once: true }}\n            className=\"text-responsive-2xl mb-4 font-poppins font-bold text-gray-900\"\n          >\n            Como o <span className=\"gradient-text\">CashBoost</span> Funciona\n          </motion.h2>\n\n          <motion.p\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.2 }}\n            viewport={{ once: true }}\n            className=\"text-responsive-sm mx-auto max-w-2xl text-gray-600\"\n          >\n            Pare de perder tempo verificando vários sites de cashback. Nossa plataforma faz o\n            trabalho pesado, para que você possa focar no que mais importa - economizar dinheiro.\n          </motion.p>\n        </div>\n\n        {/* Steps */}\n        <div className=\"mb-16 grid grid-cols-1 gap-8 lg:grid-cols-3 lg:gap-12\">\n          {steps.map((step, index) => (\n            <motion.div\n              key={step.id}\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.2 }}\n              viewport={{ once: true }}\n              className=\"relative\"\n            >\n              {/* Connection Line (Desktop) */}\n              {index < steps.length - 1 && (\n                <div className=\"absolute left-full top-16 z-0 hidden h-0.5 w-12 bg-gradient-to-r from-emerald-200 to-emerald-300 lg:block\" />\n              )}\n\n              <div className=\"relative z-10 text-center\">\n                {/* Icon */}\n                <motion.div\n                  whileHover={{ scale: 1.05, rotate: 5 }}\n                  transition={{ duration: 0.2 }}\n                  className={`inline-flex h-20 w-20 items-center justify-center rounded-2xl bg-gradient-to-br ${step.color} group mb-6 cursor-pointer shadow-lg`}\n                >\n                  <step.icon className=\"h-10 w-10 text-white\" />\n                </motion.div>\n\n                {/* Step Number */}\n                <div className=\"absolute -right-2 -top-2 flex h-8 w-8 items-center justify-center rounded-full bg-white shadow-md\">\n                  <span className=\"text-sm font-bold text-emerald-600\">{step.id}</span>\n                </div>\n\n                {/* Content */}\n                <h3 className=\"mb-4 font-poppins text-xl font-bold text-gray-900\">{step.title}</h3>\n\n                <p className=\"mb-6 leading-relaxed text-gray-600\">{step.description}</p>\n\n                {/* Features */}\n                <div className=\"space-y-2\">\n                  {step.features.map((feature, featureIndex) => (\n                    <motion.div\n                      key={feature}\n                      initial={{ opacity: 0, x: -20 }}\n                      whileInView={{ opacity: 1, x: 0 }}\n                      transition={{ duration: 0.4, delay: index * 0.2 + featureIndex * 0.1 }}\n                      viewport={{ once: true }}\n                      className=\"mb-2 mr-2 inline-flex items-center rounded-full bg-white/60 px-3 py-1 text-sm font-medium text-emerald-700 backdrop-blur-sm\"\n                    >\n                      <div className=\"mr-2 h-1.5 w-1.5 rounded-full bg-emerald-500\" />\n                      {feature}\n                    </motion.div>\n                  ))}\n                </div>\n              </div>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Benefits Grid */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          viewport={{ once: true }}\n          className=\"rounded-3xl bg-white/80 p-8 shadow-xl backdrop-blur-sm lg:p-12\"\n        >\n          <div className=\"mb-8 text-center\">\n            <h3 className=\"mb-4 font-poppins text-2xl font-bold text-gray-900\">\n              Por que o CashBoost se Destaca\n            </h3>\n            <p className=\"text-gray-600\">\n              Junte-se a milhares de compradores inteligentes que já estão maximizando seus ganhos\n              de cashback\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3\">\n            {benefits.map((benefit, index) => (\n              <motion.div\n                key={benefit}\n                initial={{ opacity: 0, scale: 0.9 }}\n                whileInView={{ opacity: 1, scale: 1 }}\n                transition={{ duration: 0.4, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                className=\"flex items-center space-x-3 rounded-xl p-4 transition-colors duration-200 hover:bg-emerald-50/50\"\n              >\n                <div className=\"flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-emerald-500\">\n                  <svg className=\"h-3 w-3 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path\n                      fillRule=\"evenodd\"\n                      d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\n                      clipRule=\"evenodd\"\n                    />\n                  </svg>\n                </div>\n                <span className=\"font-medium text-gray-700\">{benefit}</span>\n              </motion.div>\n            ))}\n          </div>\n        </motion.div>\n\n        {/* CTA */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.6 }}\n          viewport={{ once: true }}\n          className=\"mt-12 text-center\"\n        >\n          <button className=\"btn-primary animate-shine px-8 py-4 text-lg\">\n            Comece a Comparar Taxas Agora\n          </button>\n          <p className=\"mt-4 text-sm text-gray-500\">\n            Gratuito para usar • Sem cadastro necessário • Resultados instantâneos\n          </p>\n        </motion.div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAMA;AARA;;;;AAUA,MAAM,QAAQ;IACZ;QACE,IAAI;QACJ,OAAO;QACP,aACE;QACF,MAAM,wOAAA,CAAA,sBAAmB;QACzB,OAAO;QACP,UAAU;YAAC;YAAc;YAAuB;SAAuB;IACzE;IACA;QACE,IAAI;QACJ,OAAO;QACP,aACE;QACF,MAAM,0NAAA,CAAA,eAAY;QAClB,OAAO;QACP,UAAU;YAAC;YAA0B;YAAyB;SAAkB;IAClF;IACA;QACE,IAAI;QACJ,OAAO;QACP,aACE;QACF,MAAM,sOAAA,CAAA,qBAAkB;QACxB,OAAO;QACP,UAAU;YAAC;YAAkB;YAA4B;SAAoB;IAC/E;CACD;AAED,MAAM,WAAW;IACf;IACA;IACA;IACA;IACA;IACA;CACD;AAEc,SAAS;IACtB,qBACE,6LAAC;QACC,IAAG;QACH,WAAU;kBAEV,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,6LAAC,0NAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAI3C,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;gCACX;8CACQ,6LAAC;oCAAK,WAAU;8CAAgB;;;;;;gCAAgB;;;;;;;sCAGzD,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;sCACX;;;;;;;;;;;;8BAOH,6LAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;gCAGT,QAAQ,MAAM,MAAM,GAAG,mBACtB,6LAAC;oCAAI,WAAU;;;;;;8CAGjB,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,YAAY;gDAAE,OAAO;gDAAM,QAAQ;4CAAE;4CACrC,YAAY;gDAAE,UAAU;4CAAI;4CAC5B,WAAW,CAAC,gFAAgF,EAAE,KAAK,KAAK,CAAC,oCAAoC,CAAC;sDAE9I,cAAA,6LAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;;;;;;sDAIvB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAsC,KAAK,EAAE;;;;;;;;;;;sDAI/D,6LAAC;4CAAG,WAAU;sDAAqD,KAAK,KAAK;;;;;;sDAE7E,6LAAC;4CAAE,WAAU;sDAAsC,KAAK,WAAW;;;;;;sDAGnE,6LAAC;4CAAI,WAAU;sDACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC3B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oDAET,SAAS;wDAAE,SAAS;wDAAG,GAAG,CAAC;oDAAG;oDAC9B,aAAa;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAChC,YAAY;wDAAE,UAAU;wDAAK,OAAO,QAAQ,MAAM,eAAe;oDAAI;oDACrE,UAAU;wDAAE,MAAM;oDAAK;oDACvB,WAAU;;sEAEV,6LAAC;4DAAI,WAAU;;;;;;wDACd;;mDARI;;;;;;;;;;;;;;;;;2BApCR,KAAK,EAAE;;;;;;;;;;8BAsDlB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAqD;;;;;;8CAGnE,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAM/B,6LAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,aAAa;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCACpC,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;gDAAqB,MAAK;gDAAe,SAAQ;0DAC9D,cAAA,6LAAC;oDACC,UAAS;oDACT,GAAE;oDACF,UAAS;;;;;;;;;;;;;;;;sDAIf,6LAAC;4CAAK,WAAU;sDAA6B;;;;;;;mCAhBxC;;;;;;;;;;;;;;;;8BAuBb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC;4BAAO,WAAU;sCAA8C;;;;;;sCAGhE,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;;;;;;;;;;;;AAOpD;KAhKwB", "debugId": null}}, {"offset": {"line": 1429, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspace/novo/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx'\n\n/**\n * Utility function to merge class names with clsx\n */\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs)\n}\n\n/**\n * Format currency values for Brazilian Real\n */\nexport function formatCurrency(value: number): string {\n  return new Intl.NumberFormat('pt-BR', {\n    style: 'currency',\n    currency: 'BRL',\n  }).format(value)\n}\n\n/**\n * Format percentage values\n */\nexport function formatPercentage(value: number, decimals: number = 1): string {\n  return `${value.toFixed(decimals)}%`\n}\n\n/**\n * Format relative time in Portuguese\n */\nexport function formatRelativeTime(date: string | Date): string {\n  const now = new Date()\n  const targetDate = typeof date === 'string' ? new Date(date) : date\n  const diffInHours = Math.floor((now.getTime() - targetDate.getTime()) / (1000 * 60 * 60))\n  \n  if (diffInHours < 1) {\n    return 'há poucos minutos'\n  } else if (diffInHours < 24) {\n    return `há ${diffInHours} hora${diffInHours > 1 ? 's' : ''}`\n  } else {\n    const diffInDays = Math.floor(diffInHours / 24)\n    return `há ${diffInDays} dia${diffInDays > 1 ? 's' : ''}`\n  }\n}\n\n/**\n * Debounce function for search inputs\n */\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\n/**\n * Generate unique ID\n */\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9)\n}\n\n/**\n * Validate email format\n */\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\n/**\n * Truncate text with ellipsis\n */\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text\n  return text.slice(0, maxLength) + '...'\n}\n\n/**\n * Calculate best cashback rate from platforms\n */\nexport function getBestRate(platforms: Array<{ rate: number }>): number {\n  return Math.max(...platforms.map(p => p.rate))\n}\n\n/**\n * Calculate average cashback rate\n */\nexport function getAverageRate(platforms: Array<{ rate: number }>): number {\n  const sum = platforms.reduce((acc, p) => acc + p.rate, 0)\n  return sum / platforms.length\n}\n\n/**\n * Sort platforms by rate (descending)\n */\nexport function sortByRate<T extends { rate: number }>(platforms: T[]): T[] {\n  return [...platforms].sort((a, b) => b.rate - a.rate)\n}\n\n/**\n * Get trend icon based on trend type\n */\nexport function getTrendColor(trend: 'up' | 'down' | 'stable'): string {\n  switch (trend) {\n    case 'up':\n      return 'text-green-500'\n    case 'down':\n      return 'text-red-500'\n    case 'stable':\n      return 'text-gray-500'\n    default:\n      return 'text-gray-500'\n  }\n}\n\n/**\n * Sanitize search query\n */\nexport function sanitizeSearchQuery(query: string): string {\n  return query.trim().toLowerCase().replace(/[^\\w\\s]/gi, '')\n}\n\n/**\n * Check if device is mobile\n */\nexport function isMobile(): boolean {\n  if (typeof window === 'undefined') return false\n  return window.innerWidth < 768\n}\n\n/**\n * Scroll to element smoothly\n */\nexport function scrollToElement(elementId: string): void {\n  const element = document.getElementById(elementId)\n  if (element) {\n    element.scrollIntoView({ behavior: 'smooth' })\n  }\n}\n\n/**\n * Copy text to clipboard\n */\nexport async function copyToClipboard(text: string): Promise<boolean> {\n  try {\n    await navigator.clipboard.writeText(text)\n    return true\n  } catch (error) {\n    console.error('Failed to copy to clipboard:', error)\n    return false\n  }\n}\n\n/**\n * Format number with thousands separator\n */\nexport function formatNumber(num: number): string {\n  return new Intl.NumberFormat('pt-BR').format(num)\n}\n\n/**\n * Get platform color based on name\n */\nexport function getPlatformColor(platformName: string): string {\n  const colors: Record<string, string> = {\n    'meliuz': '#8B5CF6',\n    'rakuten': '#3B82F6',\n    'topcashback': '#10B981',\n    'honey': '#F59E0B',\n    'inter': '#FF6B35',\n    'pan': '#0066CC',\n  }\n  \n  const key = platformName.toLowerCase().replace(/\\s+/g, '')\n  return colors[key] || '#6B7280'\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;;AAKO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACd;AAKO,SAAS,eAAe,KAAa;IAC1C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAKO,SAAS,iBAAiB,KAAa,EAAE,WAAmB,CAAC;IAClE,OAAO,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC,CAAC;AACtC;AAKO,SAAS,mBAAmB,IAAmB;IACpD,MAAM,MAAM,IAAI;IAChB,MAAM,aAAa,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC/D,MAAM,cAAc,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,WAAW,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,EAAE;IAEvF,IAAI,cAAc,GAAG;QACnB,OAAO;IACT,OAAO,IAAI,cAAc,IAAI;QAC3B,OAAO,CAAC,GAAG,EAAE,YAAY,KAAK,EAAE,cAAc,IAAI,MAAM,IAAI;IAC9D,OAAO;QACL,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;QAC5C,OAAO,CAAC,GAAG,EAAE,WAAW,IAAI,EAAE,aAAa,IAAI,MAAM,IAAI;IAC3D;AACF;AAKO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAKO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAKO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAKO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAKO,SAAS,YAAY,SAAkC;IAC5D,OAAO,KAAK,GAAG,IAAI,UAAU,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;AAC9C;AAKO,SAAS,eAAe,SAAkC;IAC/D,MAAM,MAAM,UAAU,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,IAAI,EAAE;IACvD,OAAO,MAAM,UAAU,MAAM;AAC/B;AAKO,SAAS,WAAuC,SAAc;IACnE,OAAO;WAAI;KAAU,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,IAAI,GAAG,EAAE,IAAI;AACtD;AAKO,SAAS,cAAc,KAA+B;IAC3D,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAKO,SAAS,oBAAoB,KAAa;IAC/C,OAAO,MAAM,IAAI,GAAG,WAAW,GAAG,OAAO,CAAC,aAAa;AACzD;AAKO,SAAS;IACd,uCAAmC;;IAAW;IAC9C,OAAO,OAAO,UAAU,GAAG;AAC7B;AAKO,SAAS,gBAAgB,SAAiB;IAC/C,MAAM,UAAU,SAAS,cAAc,CAAC;IACxC,IAAI,SAAS;QACX,QAAQ,cAAc,CAAC;YAAE,UAAU;QAAS;IAC9C;AACF;AAKO,eAAe,gBAAgB,IAAY;IAChD,IAAI;QACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;QACpC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;IACT;AACF;AAKO,SAAS,aAAa,GAAW;IACtC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;AAC/C;AAKO,SAAS,iBAAiB,YAAoB;IACnD,MAAM,SAAiC;QACrC,UAAU;QACV,WAAW;QACX,eAAe;QACf,SAAS;QACT,SAAS;QACT,OAAO;IACT;IAEA,MAAM,MAAM,aAAa,WAAW,GAAG,OAAO,CAAC,QAAQ;IACvD,OAAO,MAAM,CAAC,IAAI,IAAI;AACxB", "debugId": null}}, {"offset": {"line": 1568, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspace/novo/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react'\nimport { motion } from 'framer-motion'\nimport { cn } from '@/lib/utils'\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'\n  size?: 'sm' | 'md' | 'lg'\n  loading?: boolean\n  children: React.ReactNode\n}\n\nconst buttonVariants = {\n  primary: 'btn-primary',\n  secondary: 'btn-secondary',\n  outline: 'border-2 border-emerald-500 text-emerald-600 hover:bg-emerald-50',\n  ghost: 'text-emerald-600 hover:bg-emerald-50',\n}\n\nconst buttonSizes = {\n  sm: 'px-4 py-2 text-sm',\n  md: 'px-6 py-3 text-base',\n  lg: 'px-8 py-4 text-lg',\n}\n\nexport function Button({\n  variant = 'primary',\n  size = 'md',\n  loading = false,\n  className,\n  children,\n  disabled,\n  ...props\n}: ButtonProps) {\n  return (\n    <motion.button\n      whileHover={{ scale: disabled || loading ? 1 : 1.02 }}\n      whileTap={{ scale: disabled || loading ? 1 : 0.98 }}\n      className={cn(\n        'inline-flex items-center justify-center font-semibold rounded-xl transition-all duration-300 focus-visible disabled:opacity-50 disabled:cursor-not-allowed',\n        buttonVariants[variant],\n        buttonSizes[size],\n        className\n      )}\n      disabled={disabled || loading}\n      {...props}\n    >\n      {loading && (\n        <svg\n          className=\"animate-spin -ml-1 mr-3 h-5 w-5\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n          fill=\"none\"\n          viewBox=\"0 0 24 24\"\n        >\n          <circle\n            className=\"opacity-25\"\n            cx=\"12\"\n            cy=\"12\"\n            r=\"10\"\n            stroke=\"currentColor\"\n            strokeWidth=\"4\"\n          />\n          <path\n            className=\"opacity-75\"\n            fill=\"currentColor\"\n            d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n          />\n        </svg>\n      )}\n      {children}\n    </motion.button>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AASA,MAAM,iBAAiB;IACrB,SAAS;IACT,WAAW;IACX,SAAS;IACT,OAAO;AACT;AAEA,MAAM,cAAc;IAClB,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEO,SAAS,OAAO,EACrB,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,UAAU,KAAK,EACf,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,GAAG,OACS;IACZ,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,YAAY;YAAE,OAAO,YAAY,UAAU,IAAI;QAAK;QACpD,UAAU;YAAE,OAAO,YAAY,UAAU,IAAI;QAAK;QAClD,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA,cAAc,CAAC,QAAQ,EACvB,WAAW,CAAC,KAAK,EACjB;QAEF,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,6LAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;KA/CgB", "debugId": null}}, {"offset": {"line": 1653, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspace/novo/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react'\nimport { motion } from 'framer-motion'\nimport { cn } from '@/lib/utils'\n\ninterface CardProps extends React.HTMLAttributes<HTMLDivElement> {\n  variant?: 'default' | 'featured' | 'glass'\n  hover?: boolean\n  children: React.ReactNode\n}\n\nconst cardVariants = {\n  default: 'card',\n  featured: 'card-featured',\n  glass: 'bg-white/20 backdrop-blur-sm border border-white/30',\n}\n\nexport function Card({\n  variant = 'default',\n  hover = true,\n  className,\n  children,\n  ...props\n}: CardProps) {\n  return (\n    <motion.div\n      whileHover={hover ? { y: -4, scale: 1.02 } : undefined}\n      transition={{ duration: 0.2 }}\n      className={cn(\n        'rounded-2xl p-6',\n        cardVariants[variant],\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </motion.div>\n  )\n}\n\nexport function CardHeader({\n  className,\n  children,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) {\n  return (\n    <div className={cn('mb-4', className)} {...props}>\n      {children}\n    </div>\n  )\n}\n\nexport function CardTitle({\n  className,\n  children,\n  ...props\n}: React.HTMLAttributes<HTMLHeadingElement>) {\n  return (\n    <h3 className={cn('text-xl font-poppins font-bold text-gray-900', className)} {...props}>\n      {children}\n    </h3>\n  )\n}\n\nexport function CardContent({\n  className,\n  children,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) {\n  return (\n    <div className={cn('', className)} {...props}>\n      {children}\n    </div>\n  )\n}\n\nexport function CardFooter({\n  className,\n  children,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) {\n  return (\n    <div className={cn('mt-4 pt-4 border-t border-gray-100', className)} {...props}>\n      {children}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;AACA;AACA;;;;AAQA,MAAM,eAAe;IACnB,SAAS;IACT,UAAU;IACV,OAAO;AACT;AAEO,SAAS,KAAK,EACnB,UAAU,SAAS,EACnB,QAAQ,IAAI,EACZ,SAAS,EACT,QAAQ,EACR,GAAG,OACO;IACV,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,YAAY,QAAQ;YAAE,GAAG,CAAC;YAAG,OAAO;QAAK,IAAI;QAC7C,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mBACA,YAAY,CAAC,QAAQ,EACrB;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;KArBgB;AAuBT,SAAS,WAAW,EACzB,SAAS,EACT,QAAQ,EACR,GAAG,OACkC;IACrC,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QAAa,GAAG,KAAK;kBAC7C;;;;;;AAGP;MAVgB;AAYT,SAAS,UAAU,EACxB,SAAS,EACT,QAAQ,EACR,GAAG,OACsC;IACzC,qBACE,6LAAC;QAAG,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAAa,GAAG,KAAK;kBACpF;;;;;;AAGP;MAVgB;AAYT,SAAS,YAAY,EAC1B,SAAS,EACT,QAAQ,EACR,GAAG,OACkC;IACrC,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,IAAI;QAAa,GAAG,KAAK;kBACzC;;;;;;AAGP;MAVgB;AAYT,SAAS,WAAW,EACzB,SAAS,EACT,QAAQ,EACR,GAAG,OACkC;IACrC,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QAAa,GAAG,KAAK;kBAC3E;;;;;;AAGP;MAVgB", "debugId": null}}, {"offset": {"line": 1753, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspace/novo/src/components/ui/Skeleton.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface SkeletonProps extends React.HTMLAttributes<HTMLDivElement> {\n  variant?: 'default' | 'card' | 'text' | 'circle'\n}\n\nconst skeletonVariants = {\n  default: 'h-4 w-full',\n  card: 'h-48 w-full',\n  text: 'h-4',\n  circle: 'h-12 w-12 rounded-full',\n}\n\nexport function Skeleton({\n  variant = 'default',\n  className,\n  ...props\n}: SkeletonProps) {\n  return (\n    <div\n      className={cn(\n        'animate-pulse bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 bg-[length:200%_100%] rounded-lg',\n        skeletonVariants[variant],\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport function SkeletonCard() {\n  return (\n    <div className=\"p-6 bg-white rounded-2xl shadow-lg\">\n      <div className=\"flex items-center space-x-4 mb-4\">\n        <Skeleton variant=\"circle\" />\n        <div className=\"space-y-2 flex-1\">\n          <Skeleton className=\"h-4 w-3/4\" />\n          <Skeleton className=\"h-3 w-1/2\" />\n        </div>\n      </div>\n      <Skeleton className=\"h-8 w-1/3 mb-4\" />\n      <div className=\"space-y-2\">\n        <Skeleton className=\"h-3 w-full\" />\n        <Skeleton className=\"h-3 w-5/6\" />\n        <Skeleton className=\"h-3 w-4/6\" />\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AACA;;;AAMA,MAAM,mBAAmB;IACvB,SAAS;IACT,MAAM;IACN,MAAM;IACN,QAAQ;AACV;AAEO,SAAS,SAAS,EACvB,UAAU,SAAS,EACnB,SAAS,EACT,GAAG,OACW;IACd,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0GACA,gBAAgB,CAAC,QAAQ,EACzB;QAED,GAAG,KAAK;;;;;;AAGf;KAfgB;AAiBT,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAS,SAAQ;;;;;;kCAClB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAS,WAAU;;;;;;0CACpB,6LAAC;gCAAS,WAAU;;;;;;;;;;;;;;;;;;0BAGxB,6LAAC;gBAAS,WAAU;;;;;;0BACpB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAS,WAAU;;;;;;kCACpB,6LAAC;wBAAS,WAAU;;;;;;kCACpB,6LAAC;wBAAS,WAAU;;;;;;;;;;;;;;;;;;AAI5B;MAlBgB", "debugId": null}}, {"offset": {"line": 1878, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspace/novo/src/components/ui/ErrorBoundary.tsx"], "sourcesContent": ["'use client'\n\nimport React, { Component, ErrorInfo, ReactNode } from 'react'\nimport { Button } from './Button'\n\ninterface Props {\n  children: ReactNode\n  fallback?: ReactNode\n}\n\ninterface State {\n  hasError: boolean\n  error?: Error\n}\n\nexport class ErrorBoundary extends Component<Props, State> {\n  constructor(props: Props) {\n    super(props)\n    this.state = { hasError: false }\n  }\n\n  static getDerivedStateFromError(error: Error): State {\n    return { hasError: true, error }\n  }\n\n  componentDidCatch(error: Error, errorInfo: ErrorInfo) {\n    console.error('ErrorBoundary caught an error:', error, errorInfo)\n    \n    // Here you could send error to monitoring service\n    // Example: Sentry.captureException(error, { extra: errorInfo })\n  }\n\n  handleReset = () => {\n    this.setState({ hasError: false, error: undefined })\n  }\n\n  render() {\n    if (this.state.hasError) {\n      if (this.props.fallback) {\n        return this.props.fallback\n      }\n\n      return (\n        <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n          <div className=\"max-w-md w-full bg-white rounded-2xl shadow-lg p-8 text-center\">\n            <div className=\"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6\">\n              <svg\n                className=\"w-8 h-8 text-red-600\"\n                fill=\"none\"\n                stroke=\"currentColor\"\n                viewBox=\"0 0 24 24\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                />\n              </svg>\n            </div>\n            \n            <h2 className=\"text-2xl font-poppins font-bold text-gray-900 mb-4\">\n              Oops! Algo deu errado\n            </h2>\n            \n            <p className=\"text-gray-600 mb-6\">\n              Encontramos um erro inesperado. Nossa equipe foi notificada e está \n              trabalhando para resolver o problema.\n            </p>\n\n            {process.env.NODE_ENV === 'development' && this.state.error && (\n              <details className=\"mb-6 text-left\">\n                <summary className=\"cursor-pointer text-sm text-gray-500 mb-2\">\n                  Detalhes do erro (desenvolvimento)\n                </summary>\n                <pre className=\"text-xs bg-gray-100 p-3 rounded overflow-auto\">\n                  {this.state.error.stack}\n                </pre>\n              </details>\n            )}\n\n            <div className=\"space-y-3\">\n              <Button onClick={this.handleReset} className=\"w-full\">\n                Tentar Novamente\n              </Button>\n              \n              <Button\n                variant=\"outline\"\n                onClick={() => window.location.href = '/'}\n                className=\"w-full\"\n              >\n                Voltar ao Início\n              </Button>\n            </div>\n          </div>\n        </div>\n      )\n    }\n\n    return this.props.children\n  }\n}\n\n// Hook version for functional components\nexport function useErrorHandler() {\n  return (error: Error, errorInfo?: ErrorInfo) => {\n    console.error('Error caught by useErrorHandler:', error, errorInfo)\n    \n    // Here you could send error to monitoring service\n    // Example: Sentry.captureException(error, { extra: errorInfo })\n  }\n}\n"], "names": [], "mappings": ";;;;AAsEa;;AApEb;AACA;AAHA;;;;AAeO,MAAM,sBAAsB,6JAAA,CAAA,YAAS;IAC1C,YAAY,KAAY,CAAE;QACxB,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YAAE,UAAU;QAAM;IACjC;IAEA,OAAO,yBAAyB,KAAY,EAAS;QACnD,OAAO;YAAE,UAAU;YAAM;QAAM;IACjC;IAEA,kBAAkB,KAAY,EAAE,SAAoB,EAAE;QACpD,QAAQ,KAAK,CAAC,kCAAkC,OAAO;IAEvD,kDAAkD;IAClD,gEAAgE;IAClE;IAEA,cAAc;QACZ,IAAI,CAAC,QAAQ,CAAC;YAAE,UAAU;YAAO,OAAO;QAAU;IACpD,EAAC;IAED,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACvB,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC5B;YAEA,qBACE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,WAAU;gCACV,MAAK;gCACL,QAAO;gCACP,SAAQ;0CAER,cAAA,6LAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,aAAa;oCACb,GAAE;;;;;;;;;;;;;;;;sCAKR,6LAAC;4BAAG,WAAU;sCAAqD;;;;;;sCAInE,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;wBAKjC,oDAAyB,iBAAiB,IAAI,CAAC,KAAK,CAAC,KAAK,kBACzD,6LAAC;4BAAQ,WAAU;;8CACjB,6LAAC;oCAAQ,WAAU;8CAA4C;;;;;;8CAG/D,6LAAC;oCAAI,WAAU;8CACZ,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK;;;;;;;;;;;;sCAK7B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAS,IAAI,CAAC,WAAW;oCAAE,WAAU;8CAAS;;;;;;8CAItD,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;oCACtC,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;QAOX;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;AAGO,SAAS;IACd,OAAO,CAAC,OAAc;QACpB,QAAQ,KAAK,CAAC,oCAAoC,OAAO;IAEzD,kDAAkD;IAClD,gEAAgE;IAClE;AACF", "debugId": null}}, {"offset": {"line": 2051, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspace/novo/src/components/ui/LazyLoad.tsx"], "sourcesContent": ["'use client'\n\nimport React, { Suspense, lazy, ComponentType } from 'react'\nimport { Skeleton } from './Skeleton'\nimport { ErrorBoundary } from './ErrorBoundary'\n\ninterface LazyLoadProps {\n  fallback?: React.ReactNode\n  errorFallback?: React.ReactNode\n  children: React.ReactNode\n}\n\n/**\n * Wrapper component for lazy loading with error boundary and loading state\n */\nexport function LazyLoad({ \n  fallback = <Skeleton variant=\"card\" />, \n  errorFallback,\n  children \n}: LazyLoadProps) {\n  return (\n    <ErrorBoundary fallback={errorFallback}>\n      <Suspense fallback={fallback}>\n        {children}\n      </Suspense>\n    </ErrorBoundary>\n  )\n}\n\n/**\n * Higher-order component for creating lazy-loaded components\n */\nexport function withLazyLoading<P extends object>(\n  importFunc: () => Promise<{ default: ComponentType<P> }>,\n  fallback?: React.ReactNode\n) {\n  const LazyComponent = lazy(importFunc)\n  \n  return function LazyLoadedComponent(props: P) {\n    return (\n      <LazyLoad fallback={fallback}>\n        <LazyComponent {...props} />\n      </LazyLoad>\n    )\n  }\n}\n\n/**\n * Lazy load components based on intersection observer\n */\nexport function LazySection({ \n  children, \n  fallback = <Skeleton variant=\"card\" className=\"h-96\" />,\n  threshold = 0.1,\n  rootMargin = '50px'\n}: {\n  children: React.ReactNode\n  fallback?: React.ReactNode\n  threshold?: number\n  rootMargin?: string\n}) {\n  const [isVisible, setIsVisible] = React.useState(false)\n  const ref = React.useRef<HTMLDivElement>(null)\n\n  React.useEffect(() => {\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        if (entry.isIntersecting) {\n          setIsVisible(true)\n          observer.disconnect()\n        }\n      },\n      { threshold, rootMargin }\n    )\n\n    if (ref.current) {\n      observer.observe(ref.current)\n    }\n\n    return () => observer.disconnect()\n  }, [threshold, rootMargin])\n\n  return (\n    <div ref={ref}>\n      {isVisible ? children : fallback}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;;;AAJA;;;;AAeO,SAAS,SAAS,EACvB,yBAAW,6LAAC,uIAAA,CAAA,WAAQ;IAAC,SAAQ;;;;;QAAS,EACtC,aAAa,EACb,QAAQ,EACM;IACd,qBACE,6LAAC,4IAAA,CAAA,gBAAa;QAAC,UAAU;kBACvB,cAAA,6LAAC,6JAAA,CAAA,WAAQ;YAAC,UAAU;sBACjB;;;;;;;;;;;AAIT;KAZgB;AAiBT,SAAS,gBACd,UAAwD,EACxD,QAA0B;IAE1B,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE;IAE3B,OAAO,SAAS,oBAAoB,KAAQ;QAC1C,qBACE,6LAAC;YAAS,UAAU;sBAClB,cAAA,6LAAC;gBAAe,GAAG,KAAK;;;;;;;;;;;IAG9B;AACF;AAKO,SAAS,YAAY,EAC1B,QAAQ,EACR,yBAAW,6LAAC,uIAAA,CAAA,WAAQ;IAAC,SAAQ;IAAO,WAAU;;;;;QAAS,EACvD,YAAY,GAAG,EACf,aAAa,MAAM,EAMpB;;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IACjD,MAAM,MAAM,6JAAA,CAAA,UAAK,CAAC,MAAM,CAAiB;IAEzC,6JAAA,CAAA,UAAK,CAAC,SAAS;iCAAC;YACd,MAAM,WAAW,IAAI;yCACnB,CAAC,CAAC,MAAM;oBACN,IAAI,MAAM,cAAc,EAAE;wBACxB,aAAa;wBACb,SAAS,UAAU;oBACrB;gBACF;wCACA;gBAAE;gBAAW;YAAW;YAG1B,IAAI,IAAI,OAAO,EAAE;gBACf,SAAS,OAAO,CAAC,IAAI,OAAO;YAC9B;YAEA;yCAAO,IAAM,SAAS,UAAU;;QAClC;gCAAG;QAAC;QAAW;KAAW;IAE1B,qBACE,6LAAC;QAAI,KAAK;kBACP,YAAY,WAAW;;;;;;AAG9B;GArCgB;MAAA", "debugId": null}}, {"offset": {"line": 2167, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspace/novo/src/hooks/useLocalStorage.ts"], "sourcesContent": ["import { useState, useEffect } from 'react'\n\n/**\n * Custom hook for localStorage with SSR support\n */\nexport function useLocalStorage<T>(\n  key: string,\n  initialValue: T\n): [T, (value: T | ((val: T) => T)) => void] {\n  // State to store our value\n  const [storedValue, setStoredValue] = useState<T>(() => {\n    if (typeof window === 'undefined') {\n      return initialValue\n    }\n    \n    try {\n      const item = window.localStorage.getItem(key)\n      return item ? JSON.parse(item) : initialValue\n    } catch (error) {\n      console.error(`Error reading localStorage key \"${key}\":`, error)\n      return initialValue\n    }\n  })\n\n  // Return a wrapped version of useState's setter function that persists the new value to localStorage\n  const setValue = (value: T | ((val: T) => T)) => {\n    try {\n      // Allow value to be a function so we have the same API as useState\n      const valueToStore = value instanceof Function ? value(storedValue) : value\n      setStoredValue(valueToStore)\n      \n      // Save to localStorage\n      if (typeof window !== 'undefined') {\n        window.localStorage.setItem(key, JSON.stringify(valueToStore))\n      }\n    } catch (error) {\n      console.error(`Error setting localStorage key \"${key}\":`, error)\n    }\n  }\n\n  return [storedValue, setValue]\n}\n"], "names": [], "mappings": ";;;AAAA;;;AAKO,SAAS,gBACd,GAAW,EACX,YAAe;;IAEf,2BAA2B;IAC3B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;oCAAK;YAChD,uCAAmC;;YAEnC;YAEA,IAAI;gBACF,MAAM,OAAO,OAAO,YAAY,CAAC,OAAO,CAAC;gBACzC,OAAO,OAAO,KAAK,KAAK,CAAC,QAAQ;YACnC,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,gCAAgC,EAAE,IAAI,EAAE,CAAC,EAAE;gBAC1D,OAAO;YACT;QACF;;IAEA,qGAAqG;IACrG,MAAM,WAAW,CAAC;QAChB,IAAI;YACF,mEAAmE;YACnE,MAAM,eAAe,iBAAiB,WAAW,MAAM,eAAe;YACtE,eAAe;YAEf,uBAAuB;YACvB,wCAAmC;gBACjC,OAAO,YAAY,CAAC,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC;YAClD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,gCAAgC,EAAE,IAAI,EAAE,CAAC,EAAE;QAC5D;IACF;IAEA,OAAO;QAAC;QAAa;KAAS;AAChC;GApCgB", "debugId": null}}, {"offset": {"line": 2219, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspace/novo/src/constants/index.ts"], "sourcesContent": ["// Application constants\n\nexport const APP_CONFIG = {\n  name: 'CashBoost',\n  description: 'Compare taxas de cashback e maximize suas economias',\n  url: 'https://cashboost.com.br',\n  version: '1.0.0',\n} as const\n\nexport const BREAKPOINTS = {\n  mobile: 768,\n  tablet: 1024,\n  desktop: 1280,\n} as const\n\nexport const ANIMATION_DURATION = {\n  fast: 200,\n  normal: 300,\n  slow: 500,\n} as const\n\nexport const SEARCH_CONFIG = {\n  debounceDelay: 300,\n  minQueryLength: 2,\n  maxResults: 50,\n} as const\n\nexport const PAGINATION_CONFIG = {\n  defaultPageSize: 12,\n  maxPageSize: 50,\n} as const\n\nexport const CACHE_KEYS = {\n  stores: 'stores',\n  platforms: 'platforms',\n  rates: 'rates',\n  search: 'search',\n} as const\n\nexport const API_ENDPOINTS = {\n  stores: '/api/stores',\n  platforms: '/api/platforms',\n  rates: '/api/rates',\n  search: '/api/search',\n} as const\n\nexport const STORAGE_KEYS = {\n  theme: 'cashboost-theme',\n  preferences: 'cashboost-preferences',\n  favorites: 'cashboost-favorites',\n} as const\n\nexport const SOCIAL_LINKS = {\n  twitter: 'https://twitter.com/cashboost',\n  instagram: 'https://instagram.com/cashboost',\n  linkedin: 'https://linkedin.com/company/cashboost',\n} as const\n\nexport const CONTACT_INFO = {\n  email: '<EMAIL>',\n  phone: '+55 11 99999-9999',\n  address: 'São Paulo, SP - Brasil',\n} as const\n"], "names": [], "mappings": "AAAA,wBAAwB;;;;;;;;;;;;;AAEjB,MAAM,aAAa;IACxB,MAAM;IACN,aAAa;IACb,KAAK;IACL,SAAS;AACX;AAEO,MAAM,cAAc;IACzB,QAAQ;IACR,QAAQ;IACR,SAAS;AACX;AAEO,MAAM,qBAAqB;IAChC,MAAM;IACN,QAAQ;IACR,MAAM;AACR;AAEO,MAAM,gBAAgB;IAC3B,eAAe;IACf,gBAAgB;IAChB,YAAY;AACd;AAEO,MAAM,oBAAoB;IAC/B,iBAAiB;IACjB,aAAa;AACf;AAEO,MAAM,aAAa;IACxB,QAAQ;IACR,WAAW;IACX,OAAO;IACP,QAAQ;AACV;AAEO,MAAM,gBAAgB;IAC3B,QAAQ;IACR,WAAW;IACX,OAAO;IACP,QAAQ;AACV;AAEO,MAAM,eAAe;IAC1B,OAAO;IACP,aAAa;IACb,WAAW;AACb;AAEO,MAAM,eAAe;IAC1B,SAAS;IACT,WAAW;IACX,UAAU;AACZ;AAEO,MAAM,eAAe;IAC1B,OAAO;IACP,OAAO;IACP,SAAS;AACX", "debugId": null}}, {"offset": {"line": 2293, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspace/novo/src/hooks/useTheme.ts"], "sourcesContent": ["import { useState, useEffect } from 'react'\nimport { useLocalStorage } from './useLocalStorage'\nimport { STORAGE_KEYS } from '@/constants'\n\nexport type Theme = 'light' | 'dark' | 'system'\n\ninterface ThemeContextType {\n  theme: Theme\n  resolvedTheme: 'light' | 'dark'\n  setTheme: (theme: Theme) => void\n  toggleTheme: () => void\n}\n\n/**\n * Custom hook for theme management with system preference detection\n */\nexport function useTheme(): ThemeContextType {\n  const [storedTheme, setStoredTheme] = useLocalStorage<Theme>(STORAGE_KEYS.theme, 'system')\n  const [systemTheme, setSystemTheme] = useState<'light' | 'dark'>('light')\n\n  // Detect system theme preference\n  useEffect(() => {\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')\n    \n    const handleChange = (e: MediaQueryListEvent) => {\n      setSystemTheme(e.matches ? 'dark' : 'light')\n    }\n\n    // Set initial value\n    setSystemTheme(mediaQuery.matches ? 'dark' : 'light')\n\n    // Listen for changes\n    mediaQuery.addEventListener('change', handleChange)\n\n    return () => mediaQuery.removeEventListener('change', handleChange)\n  }, [])\n\n  // Apply theme to document\n  useEffect(() => {\n    const root = document.documentElement\n    const resolvedTheme = storedTheme === 'system' ? systemTheme : storedTheme\n\n    if (resolvedTheme === 'dark') {\n      root.classList.add('dark')\n    } else {\n      root.classList.remove('dark')\n    }\n\n    // Update meta theme-color\n    const metaThemeColor = document.querySelector('meta[name=\"theme-color\"]')\n    if (metaThemeColor) {\n      metaThemeColor.setAttribute(\n        'content', \n        resolvedTheme === 'dark' ? '#1F2937' : '#10B981'\n      )\n    }\n  }, [storedTheme, systemTheme])\n\n  const resolvedTheme = storedTheme === 'system' ? systemTheme : storedTheme\n\n  const setTheme = (theme: Theme) => {\n    setStoredTheme(theme)\n  }\n\n  const toggleTheme = () => {\n    if (storedTheme === 'light') {\n      setTheme('dark')\n    } else if (storedTheme === 'dark') {\n      setTheme('system')\n    } else {\n      setTheme('light')\n    }\n  }\n\n  return {\n    theme: storedTheme,\n    resolvedTheme,\n    setTheme,\n    toggleTheme,\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;;AAcO,SAAS;;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAS,4HAAA,CAAA,eAAY,CAAC,KAAK,EAAE;IACjF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IAEjE,iCAAiC;IACjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,MAAM,aAAa,OAAO,UAAU,CAAC;YAErC,MAAM;mDAAe,CAAC;oBACpB,eAAe,EAAE,OAAO,GAAG,SAAS;gBACtC;;YAEA,oBAAoB;YACpB,eAAe,WAAW,OAAO,GAAG,SAAS;YAE7C,qBAAqB;YACrB,WAAW,gBAAgB,CAAC,UAAU;YAEtC;sCAAO,IAAM,WAAW,mBAAmB,CAAC,UAAU;;QACxD;6BAAG,EAAE;IAEL,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,MAAM,OAAO,SAAS,eAAe;YACrC,MAAM,gBAAgB,gBAAgB,WAAW,cAAc;YAE/D,IAAI,kBAAkB,QAAQ;gBAC5B,KAAK,SAAS,CAAC,GAAG,CAAC;YACrB,OAAO;gBACL,KAAK,SAAS,CAAC,MAAM,CAAC;YACxB;YAEA,0BAA0B;YAC1B,MAAM,iBAAiB,SAAS,aAAa,CAAC;YAC9C,IAAI,gBAAgB;gBAClB,eAAe,YAAY,CACzB,WACA,kBAAkB,SAAS,YAAY;YAE3C;QACF;6BAAG;QAAC;QAAa;KAAY;IAE7B,MAAM,gBAAgB,gBAAgB,WAAW,cAAc;IAE/D,MAAM,WAAW,CAAC;QAChB,eAAe;IACjB;IAEA,MAAM,cAAc;QAClB,IAAI,gBAAgB,SAAS;YAC3B,SAAS;QACX,OAAO,IAAI,gBAAgB,QAAQ;YACjC,SAAS;QACX,OAAO;YACL,SAAS;QACX;IACF;IAEA,OAAO;QACL,OAAO;QACP;QACA;QACA;IACF;AACF;GAhEgB;;QACwB,kIAAA,CAAA,kBAAe", "debugId": null}}, {"offset": {"line": 2379, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspace/novo/src/components/ui/ThemeToggle.tsx"], "sourcesContent": ["'use client'\n\nimport { memo } from 'react'\nimport { motion } from 'framer-motion'\nimport { \n  SunIcon, \n  MoonIcon, \n  ComputerDesktopIcon \n} from '@heroicons/react/24/outline'\nimport { useTheme } from '@/hooks/useTheme'\nimport { cn } from '@/lib/utils'\n\nconst themeIcons = {\n  light: SunIcon,\n  dark: MoonIcon,\n  system: ComputerDesktopIcon,\n}\n\nconst themeLabels = {\n  light: 'Claro',\n  dark: 'Escuro',\n  system: 'Sistema',\n}\n\ninterface ThemeToggleProps {\n  variant?: 'button' | 'dropdown'\n  className?: string\n}\n\nexport const ThemeToggle = memo(({ \n  variant = 'button', \n  className \n}: ThemeToggleProps) => {\n  const { theme, toggleTheme, setTheme } = useTheme()\n\n  if (variant === 'dropdown') {\n    return (\n      <div className={cn('relative', className)}>\n        <select\n          value={theme}\n          onChange={(e) => setTheme(e.target.value as any)}\n          className=\"appearance-none bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500\"\n        >\n          {Object.entries(themeLabels).map(([value, label]) => (\n            <option key={value} value={value}>\n              {label}\n            </option>\n          ))}\n        </select>\n      </div>\n    )\n  }\n\n  const Icon = themeIcons[theme]\n\n  return (\n    <motion.button\n      whileHover={{ scale: 1.05 }}\n      whileTap={{ scale: 0.95 }}\n      onClick={toggleTheme}\n      className={cn(\n        'inline-flex items-center justify-center w-10 h-10 rounded-lg',\n        'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700',\n        'text-gray-700 dark:text-gray-300 hover:text-emerald-600 dark:hover:text-emerald-400',\n        'transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-emerald-500',\n        className\n      )}\n      aria-label={`Alternar tema (atual: ${themeLabels[theme]})`}\n      title={`Tema atual: ${themeLabels[theme]}`}\n    >\n      <Icon className=\"w-5 h-5\" />\n    </motion.button>\n  )\n})\n\nThemeToggle.displayName = 'ThemeToggle'\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAKA;AACA;;;AAVA;;;;;;AAYA,MAAM,aAAa;IACjB,OAAO,gNAAA,CAAA,UAAO;IACd,MAAM,kNAAA,CAAA,WAAQ;IACd,QAAQ,wOAAA,CAAA,sBAAmB;AAC7B;AAEA,MAAM,cAAc;IAClB,OAAO;IACP,MAAM;IACN,QAAQ;AACV;AAOO,MAAM,4BAAc,GAAA,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,UAAE,CAAC,EAC/B,UAAU,QAAQ,EAClB,SAAS,EACQ;;IACjB,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,WAAQ,AAAD;IAEhD,IAAI,YAAY,YAAY;QAC1B,qBACE,6LAAC;YAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;sBAC7B,cAAA,6LAAC;gBACC,OAAO;gBACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gBACxC,WAAU;0BAET,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC,OAAO,MAAM,iBAC9C,6LAAC;wBAAmB,OAAO;kCACxB;uBADU;;;;;;;;;;;;;;;IAOvB;IAEA,MAAM,OAAO,UAAU,CAAC,MAAM;IAE9B,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,YAAY;YAAE,OAAO;QAAK;QAC1B,UAAU;YAAE,OAAO;QAAK;QACxB,SAAS;QACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gEACA,yEACA,uFACA,yFACA;QAEF,cAAY,CAAC,sBAAsB,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC;QAC1D,OAAO,CAAC,YAAY,EAAE,WAAW,CAAC,MAAM,EAAE;kBAE1C,cAAA,6LAAC;YAAK,WAAU;;;;;;;;;;;AAGtB;;QAxC2C,2HAAA,CAAA,WAAQ;;;;QAAR,2HAAA,CAAA,WAAQ;;;;AA0CnD,YAAY,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2484, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspace/novo/src/components/ui/SkipLink.tsx"], "sourcesContent": ["'use client'\n\nimport { cn } from '@/lib/utils'\n\ninterface SkipLinkProps {\n  href: string\n  children: React.ReactNode\n  className?: string\n}\n\n/**\n * Skip link component for keyboard navigation accessibility\n */\nexport function SkipLink({ href, children, className }: SkipLinkProps) {\n  return (\n    <a\n      href={href}\n      className={cn(\n        'sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50',\n        'bg-emerald-600 text-white px-4 py-2 rounded-lg font-medium',\n        'focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2',\n        'transition-all duration-200',\n        className\n      )}\n    >\n      {children}\n    </a>\n  )\n}\n\n/**\n * Skip navigation component with multiple skip links\n */\nexport function SkipNavigation() {\n  return (\n    <div className=\"sr-only focus-within:not-sr-only\">\n      <SkipLink href=\"#main-content\">\n        Pular para o conteúdo principal\n      </SkipLink>\n      <SkipLink href=\"#navigation\" className=\"ml-2\">\n        Pular para a navegação\n      </SkipLink>\n      <SkipLink href=\"#search\" className=\"ml-2\">\n        Pular para a busca\n      </SkipLink>\n      <SkipLink href=\"#footer\" className=\"ml-2\">\n        Pular para o rodapé\n      </SkipLink>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAaO,SAAS,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAiB;IACnE,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gFACA,8DACA,8EACA,+BACA;kBAGD;;;;;;AAGP;KAfgB;AAoBT,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAS,MAAK;0BAAgB;;;;;;0BAG/B,6LAAC;gBAAS,MAAK;gBAAc,WAAU;0BAAO;;;;;;0BAG9C,6LAAC;gBAAS,MAAK;gBAAU,WAAU;0BAAO;;;;;;0BAG1C,6LAAC;gBAAS,MAAK;gBAAU,WAAU;0BAAO;;;;;;;;;;;;AAKhD;MAjBgB", "debugId": null}}, {"offset": {"line": 2564, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspace/novo/src/components/ui/Announcer.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\n\ninterface AnnouncerProps {\n  message: string\n  priority?: 'polite' | 'assertive'\n  delay?: number\n}\n\n/**\n * Component for announcing messages to screen readers\n */\nexport function Announcer({ \n  message, \n  priority = 'polite', \n  delay = 0 \n}: AnnouncerProps) {\n  const [announcement, setAnnouncement] = useState('')\n\n  useEffect(() => {\n    if (!message) return\n\n    const timer = setTimeout(() => {\n      setAnnouncement(message)\n      \n      // Clear the announcement after it's been read\n      const clearTimer = setTimeout(() => {\n        setAnnouncement('')\n      }, 1000)\n\n      return () => clearTimeout(clearTimer)\n    }, delay)\n\n    return () => clearTimeout(timer)\n  }, [message, delay])\n\n  if (!announcement) return null\n\n  return (\n    <div\n      aria-live={priority}\n      aria-atomic=\"true\"\n      className=\"sr-only\"\n    >\n      {announcement}\n    </div>\n  )\n}\n\n/**\n * Hook for programmatic announcements\n */\nexport function useAnnouncer() {\n  const [announcements, setAnnouncements] = useState<Array<{\n    id: string\n    message: string\n    priority: 'polite' | 'assertive'\n  }>>([])\n\n  const announce = (\n    message: string, \n    priority: 'polite' | 'assertive' = 'polite'\n  ) => {\n    const id = Math.random().toString(36).substr(2, 9)\n    \n    setAnnouncements(prev => [...prev, { id, message, priority }])\n\n    // Remove announcement after delay\n    setTimeout(() => {\n      setAnnouncements(prev => prev.filter(a => a.id !== id))\n    }, 1000)\n  }\n\n  const AnnouncerComponent = () => (\n    <>\n      {announcements.map(({ id, message, priority }) => (\n        <Announcer\n          key={id}\n          message={message}\n          priority={priority}\n        />\n      ))}\n    </>\n  )\n\n  return {\n    announce,\n    AnnouncerComponent,\n  }\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAaO,SAAS,UAAU,EACxB,OAAO,EACP,WAAW,QAAQ,EACnB,QAAQ,CAAC,EACM;;IACf,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,CAAC,SAAS;YAEd,MAAM,QAAQ;6CAAW;oBACvB,gBAAgB;oBAEhB,8CAA8C;oBAC9C,MAAM,aAAa;gEAAW;4BAC5B,gBAAgB;wBAClB;+DAAG;oBAEH;qDAAO,IAAM,aAAa;;gBAC5B;4CAAG;YAEH;uCAAO,IAAM,aAAa;;QAC5B;8BAAG;QAAC;QAAS;KAAM;IAEnB,IAAI,CAAC,cAAc,OAAO;IAE1B,qBACE,6LAAC;QACC,aAAW;QACX,eAAY;QACZ,WAAU;kBAET;;;;;;AAGP;GAnCgB;KAAA;AAwCT,SAAS;;IACd,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAI7C,EAAE;IAEN,MAAM,WAAW,CACf,SACA,WAAmC,QAAQ;QAE3C,MAAM,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;QAEhD,iBAAiB,CAAA,OAAQ;mBAAI;gBAAM;oBAAE;oBAAI;oBAAS;gBAAS;aAAE;QAE7D,kCAAkC;QAClC,WAAW;YACT,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACrD,GAAG;IACL;IAEA,MAAM,qBAAqB,kBACzB;sBACG,cAAc,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,OAAO,EAAE,QAAQ,EAAE,iBAC3C,6LAAC;oBAEC,SAAS;oBACT,UAAU;mBAFL;;;;;;IAQb,OAAO;QACL;QACA;IACF;AACF;IArCgB", "debugId": null}}, {"offset": {"line": 2661, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspace/novo/src/components/ui/Tooltip.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useRef, useId } from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { cn } from '@/lib/utils'\n\ninterface TooltipProps {\n  content: string\n  children: React.ReactElement\n  placement?: 'top' | 'bottom' | 'left' | 'right'\n  delay?: number\n  className?: string\n}\n\n/**\n * Accessible tooltip component with ARIA support\n */\nexport function Tooltip({\n  content,\n  children,\n  placement = 'top',\n  delay = 500,\n  className,\n}: TooltipProps) {\n  const [isVisible, setIsVisible] = useState(false)\n  const [isHovered, setIsHovered] = useState(false)\n  const [isFocused, setIsFocused] = useState(false)\n  const timeoutRef = useRef<NodeJS.Timeout>()\n  const tooltipId = useId()\n\n  const showTooltip = () => {\n    clearTimeout(timeoutRef.current)\n    timeoutRef.current = setTimeout(() => {\n      setIsVisible(true)\n    }, delay)\n  }\n\n  const hideTooltip = () => {\n    clearTimeout(timeoutRef.current)\n    setIsVisible(false)\n  }\n\n  const handleMouseEnter = () => {\n    setIsHovered(true)\n    showTooltip()\n  }\n\n  const handleMouseLeave = () => {\n    setIsHovered(false)\n    if (!isFocused) {\n      hideTooltip()\n    }\n  }\n\n  const handleFocus = () => {\n    setIsFocused(true)\n    showTooltip()\n  }\n\n  const handleBlur = () => {\n    setIsFocused(false)\n    if (!isHovered) {\n      hideTooltip()\n    }\n  }\n\n  const handleKeyDown = (event: React.KeyboardEvent) => {\n    if (event.key === 'Escape') {\n      hideTooltip()\n    }\n  }\n\n  const placementClasses = {\n    top: 'bottom-full left-1/2 transform -translate-x-1/2 mb-2',\n    bottom: 'top-full left-1/2 transform -translate-x-1/2 mt-2',\n    left: 'right-full top-1/2 transform -translate-y-1/2 mr-2',\n    right: 'left-full top-1/2 transform -translate-y-1/2 ml-2',\n  }\n\n  const arrowClasses = {\n    top: 'top-full left-1/2 transform -translate-x-1/2 border-l-transparent border-r-transparent border-b-transparent border-t-gray-900',\n    bottom: 'bottom-full left-1/2 transform -translate-x-1/2 border-l-transparent border-r-transparent border-t-transparent border-b-gray-900',\n    left: 'left-full top-1/2 transform -translate-y-1/2 border-t-transparent border-b-transparent border-r-transparent border-l-gray-900',\n    right: 'right-full top-1/2 transform -translate-y-1/2 border-t-transparent border-b-transparent border-l-transparent border-r-gray-900',\n  }\n\n  // Clone the child element and add event handlers\n  const triggerElement = React.cloneElement(children, {\n    onMouseEnter: handleMouseEnter,\n    onMouseLeave: handleMouseLeave,\n    onFocus: handleFocus,\n    onBlur: handleBlur,\n    onKeyDown: handleKeyDown,\n    'aria-describedby': isVisible ? tooltipId : undefined,\n    ...children.props,\n  })\n\n  return (\n    <div className=\"relative inline-block\">\n      {triggerElement}\n      \n      <AnimatePresence>\n        {isVisible && (\n          <motion.div\n            initial={{ opacity: 0, scale: 0.95 }}\n            animate={{ opacity: 1, scale: 1 }}\n            exit={{ opacity: 0, scale: 0.95 }}\n            transition={{ duration: 0.15 }}\n            id={tooltipId}\n            role=\"tooltip\"\n            className={cn(\n              'absolute z-50 px-3 py-2 text-sm text-white bg-gray-900 rounded-lg shadow-lg pointer-events-none',\n              'max-w-xs break-words',\n              placementClasses[placement],\n              className\n            )}\n          >\n            {content}\n            \n            {/* Arrow */}\n            <div\n              className={cn(\n                'absolute w-0 h-0 border-4',\n                arrowClasses[placement]\n              )}\n            />\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </div>\n  )\n}\n\n/**\n * Simple tooltip for basic use cases\n */\nexport function SimpleTooltip({\n  content,\n  children,\n}: {\n  content: string\n  children: React.ReactNode\n}) {\n  return (\n    <div className=\"group relative inline-block\">\n      {children}\n      <div className=\"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 text-sm text-white bg-gray-900 rounded-lg opacity-0 group-hover:opacity-100 group-focus-within:opacity-100 transition-opacity duration-200 pointer-events-none z-50 whitespace-nowrap\">\n        {content}\n        <div className=\"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-4 border-l-transparent border-r-transparent border-b-transparent border-t-gray-900\" />\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AACA;;;AAJA;;;;AAiBO,SAAS,QAAQ,EACtB,OAAO,EACP,QAAQ,EACR,YAAY,KAAK,EACjB,QAAQ,GAAG,EACX,SAAS,EACI;;IACb,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IACxB,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,QAAK,AAAD;IAEtB,MAAM,cAAc;QAClB,aAAa,WAAW,OAAO;QAC/B,WAAW,OAAO,GAAG,WAAW;YAC9B,aAAa;QACf,GAAG;IACL;IAEA,MAAM,cAAc;QAClB,aAAa,WAAW,OAAO;QAC/B,aAAa;IACf;IAEA,MAAM,mBAAmB;QACvB,aAAa;QACb;IACF;IAEA,MAAM,mBAAmB;QACvB,aAAa;QACb,IAAI,CAAC,WAAW;YACd;QACF;IACF;IAEA,MAAM,cAAc;QAClB,aAAa;QACb;IACF;IAEA,MAAM,aAAa;QACjB,aAAa;QACb,IAAI,CAAC,WAAW;YACd;QACF;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,MAAM,GAAG,KAAK,UAAU;YAC1B;QACF;IACF;IAEA,MAAM,mBAAmB;QACvB,KAAK;QACL,QAAQ;QACR,MAAM;QACN,OAAO;IACT;IAEA,MAAM,eAAe;QACnB,KAAK;QACL,QAAQ;QACR,MAAM;QACN,OAAO;IACT;IAEA,iDAAiD;IACjD,MAAM,+BAAiB,6JAAA,CAAA,UAAK,CAAC,YAAY,CAAC,UAAU;QAClD,cAAc;QACd,cAAc;QACd,SAAS;QACT,QAAQ;QACR,WAAW;QACX,oBAAoB,YAAY,YAAY;QAC5C,GAAG,SAAS,KAAK;IACnB;IAEA,qBACE,6LAAC;QAAI,WAAU;;YACZ;0BAED,6LAAC,4LAAA,CAAA,kBAAe;0BACb,2BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAK;oBACnC,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAE;oBAChC,MAAM;wBAAE,SAAS;wBAAG,OAAO;oBAAK;oBAChC,YAAY;wBAAE,UAAU;oBAAK;oBAC7B,IAAI;oBACJ,MAAK;oBACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mGACA,wBACA,gBAAgB,CAAC,UAAU,EAC3B;;wBAGD;sCAGD,6LAAC;4BACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6BACA,YAAY,CAAC,UAAU;;;;;;;;;;;;;;;;;;;;;;;AAQvC;GAlHgB;;QAWI,6JAAA,CAAA,QAAK;;;KAXT;AAuHT,SAAS,cAAc,EAC5B,OAAO,EACP,QAAQ,EAIT;IACC,qBACE,6LAAC;QAAI,WAAU;;YACZ;0BACD,6LAAC;gBAAI,WAAU;;oBACZ;kCACD,6LAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;AAIvB;MAhBgB", "debugId": null}}, {"offset": {"line": 2839, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspace/novo/src/hooks/useFocusManagement.ts"], "sourcesContent": ["import { useEffect, useRef } from 'react'\n\n/**\n * Hook for managing focus in modals and overlays\n */\nexport function useFocusManagement(isOpen: boolean) {\n  const containerRef = useRef<HTMLElement>(null)\n  const previousActiveElement = useRef<HTMLElement | null>(null)\n\n  useEffect(() => {\n    if (!isOpen) return\n\n    // Store the currently focused element\n    previousActiveElement.current = document.activeElement as HTMLElement\n\n    // Focus the container or first focusable element\n    const container = containerRef.current\n    if (container) {\n      const firstFocusable = container.querySelector(\n        'button, [href], input, select, textarea, [tabindex]:not([tabindex=\"-1\"])'\n      ) as HTMLElement\n\n      if (firstFocusable) {\n        firstFocusable.focus()\n      } else {\n        container.focus()\n      }\n    }\n\n    // Trap focus within the container\n    const handleKeyDown = (event: KeyboardEvent) => {\n      if (event.key !== 'Tab' || !container) return\n\n      const focusableElements = container.querySelectorAll(\n        'button:not([disabled]), [href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex=\"-1\"])'\n      ) as NodeListOf<HTMLElement>\n\n      const firstElement = focusableElements[0]\n      const lastElement = focusableElements[focusableElements.length - 1]\n\n      if (event.shiftKey) {\n        // Shift + Tab\n        if (document.activeElement === firstElement) {\n          event.preventDefault()\n          lastElement?.focus()\n        }\n      } else {\n        // Tab\n        if (document.activeElement === lastElement) {\n          event.preventDefault()\n          firstElement?.focus()\n        }\n      }\n    }\n\n    document.addEventListener('keydown', handleKeyDown)\n\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown)\n      \n      // Restore focus to the previously focused element\n      if (previousActiveElement.current) {\n        previousActiveElement.current.focus()\n      }\n    }\n  }, [isOpen])\n\n  return containerRef\n}\n\n/**\n * Hook for managing focus announcements for screen readers\n */\nexport function useFocusAnnouncement() {\n  const announce = (message: string, priority: 'polite' | 'assertive' = 'polite') => {\n    const announcement = document.createElement('div')\n    announcement.setAttribute('aria-live', priority)\n    announcement.setAttribute('aria-atomic', 'true')\n    announcement.className = 'sr-only'\n    announcement.textContent = message\n\n    document.body.appendChild(announcement)\n\n    // Remove the announcement after it's been read\n    setTimeout(() => {\n      document.body.removeChild(announcement)\n    }, 1000)\n  }\n\n  return { announce }\n}\n\n/**\n * Hook for keyboard navigation\n */\nexport function useKeyboardNavigation(\n  items: HTMLElement[],\n  options: {\n    loop?: boolean\n    orientation?: 'horizontal' | 'vertical'\n    onSelect?: (index: number) => void\n  } = {}\n) {\n  const { loop = true, orientation = 'vertical', onSelect } = options\n  const currentIndex = useRef(0)\n\n  const handleKeyDown = (event: KeyboardEvent) => {\n    const { key } = event\n    let newIndex = currentIndex.current\n\n    const isVertical = orientation === 'vertical'\n    const nextKey = isVertical ? 'ArrowDown' : 'ArrowRight'\n    const prevKey = isVertical ? 'ArrowUp' : 'ArrowLeft'\n\n    switch (key) {\n      case nextKey:\n        event.preventDefault()\n        newIndex = currentIndex.current + 1\n        if (newIndex >= items.length) {\n          newIndex = loop ? 0 : items.length - 1\n        }\n        break\n\n      case prevKey:\n        event.preventDefault()\n        newIndex = currentIndex.current - 1\n        if (newIndex < 0) {\n          newIndex = loop ? items.length - 1 : 0\n        }\n        break\n\n      case 'Home':\n        event.preventDefault()\n        newIndex = 0\n        break\n\n      case 'End':\n        event.preventDefault()\n        newIndex = items.length - 1\n        break\n\n      case 'Enter':\n      case ' ':\n        event.preventDefault()\n        onSelect?.(currentIndex.current)\n        return\n\n      default:\n        return\n    }\n\n    currentIndex.current = newIndex\n    items[newIndex]?.focus()\n  }\n\n  const setCurrentIndex = (index: number) => {\n    currentIndex.current = index\n  }\n\n  return {\n    handleKeyDown,\n    setCurrentIndex,\n    currentIndex: currentIndex.current,\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;AAKO,SAAS,mBAAmB,MAAe;;IAChD,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAe;IACzC,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAsB;IAEzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,CAAC,QAAQ;YAEb,sCAAsC;YACtC,sBAAsB,OAAO,GAAG,SAAS,aAAa;YAEtD,iDAAiD;YACjD,MAAM,YAAY,aAAa,OAAO;YACtC,IAAI,WAAW;gBACb,MAAM,iBAAiB,UAAU,aAAa,CAC5C;gBAGF,IAAI,gBAAgB;oBAClB,eAAe,KAAK;gBACtB,OAAO;oBACL,UAAU,KAAK;gBACjB;YACF;YAEA,kCAAkC;YAClC,MAAM;8DAAgB,CAAC;oBACrB,IAAI,MAAM,GAAG,KAAK,SAAS,CAAC,WAAW;oBAEvC,MAAM,oBAAoB,UAAU,gBAAgB,CAClD;oBAGF,MAAM,eAAe,iBAAiB,CAAC,EAAE;oBACzC,MAAM,cAAc,iBAAiB,CAAC,kBAAkB,MAAM,GAAG,EAAE;oBAEnE,IAAI,MAAM,QAAQ,EAAE;wBAClB,cAAc;wBACd,IAAI,SAAS,aAAa,KAAK,cAAc;4BAC3C,MAAM,cAAc;4BACpB,aAAa;wBACf;oBACF,OAAO;wBACL,MAAM;wBACN,IAAI,SAAS,aAAa,KAAK,aAAa;4BAC1C,MAAM,cAAc;4BACpB,cAAc;wBAChB;oBACF;gBACF;;YAEA,SAAS,gBAAgB,CAAC,WAAW;YAErC;gDAAO;oBACL,SAAS,mBAAmB,CAAC,WAAW;oBAExC,kDAAkD;oBAClD,IAAI,sBAAsB,OAAO,EAAE;wBACjC,sBAAsB,OAAO,CAAC,KAAK;oBACrC;gBACF;;QACF;uCAAG;QAAC;KAAO;IAEX,OAAO;AACT;GA/DgB;AAoET,SAAS;IACd,MAAM,WAAW,CAAC,SAAiB,WAAmC,QAAQ;QAC5E,MAAM,eAAe,SAAS,aAAa,CAAC;QAC5C,aAAa,YAAY,CAAC,aAAa;QACvC,aAAa,YAAY,CAAC,eAAe;QACzC,aAAa,SAAS,GAAG;QACzB,aAAa,WAAW,GAAG;QAE3B,SAAS,IAAI,CAAC,WAAW,CAAC;QAE1B,+CAA+C;QAC/C,WAAW;YACT,SAAS,IAAI,CAAC,WAAW,CAAC;QAC5B,GAAG;IACL;IAEA,OAAO;QAAE;IAAS;AACpB;AAKO,SAAS,sBACd,KAAoB,EACpB,UAII,CAAC,CAAC;;IAEN,MAAM,EAAE,OAAO,IAAI,EAAE,cAAc,UAAU,EAAE,QAAQ,EAAE,GAAG;IAC5D,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE5B,MAAM,gBAAgB,CAAC;QACrB,MAAM,EAAE,GAAG,EAAE,GAAG;QAChB,IAAI,WAAW,aAAa,OAAO;QAEnC,MAAM,aAAa,gBAAgB;QACnC,MAAM,UAAU,aAAa,cAAc;QAC3C,MAAM,UAAU,aAAa,YAAY;QAEzC,OAAQ;YACN,KAAK;gBACH,MAAM,cAAc;gBACpB,WAAW,aAAa,OAAO,GAAG;gBAClC,IAAI,YAAY,MAAM,MAAM,EAAE;oBAC5B,WAAW,OAAO,IAAI,MAAM,MAAM,GAAG;gBACvC;gBACA;YAEF,KAAK;gBACH,MAAM,cAAc;gBACpB,WAAW,aAAa,OAAO,GAAG;gBAClC,IAAI,WAAW,GAAG;oBAChB,WAAW,OAAO,MAAM,MAAM,GAAG,IAAI;gBACvC;gBACA;YAEF,KAAK;gBACH,MAAM,cAAc;gBACpB,WAAW;gBACX;YAEF,KAAK;gBACH,MAAM,cAAc;gBACpB,WAAW,MAAM,MAAM,GAAG;gBAC1B;YAEF,KAAK;YACL,KAAK;gBACH,MAAM,cAAc;gBACpB,WAAW,aAAa,OAAO;gBAC/B;YAEF;gBACE;QACJ;QAEA,aAAa,OAAO,GAAG;QACvB,KAAK,CAAC,SAAS,EAAE;IACnB;IAEA,MAAM,kBAAkB,CAAC;QACvB,aAAa,OAAO,GAAG;IACzB;IAEA,OAAO;QACL;QACA;QACA,cAAc,aAAa,OAAO;IACpC;AACF;IArEgB", "debugId": null}}, {"offset": {"line": 2985, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspace/novo/src/components/ui/Modal.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useEffect, useId } from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { XMarkIcon } from '@heroicons/react/24/outline'\nimport { useFocusManagement } from '@/hooks/useFocusManagement'\nimport { But<PERSON> } from './Button'\nimport { cn } from '@/lib/utils'\n\ninterface ModalProps {\n  isOpen: boolean\n  onClose: () => void\n  title: string\n  description?: string\n  children: React.ReactNode\n  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'\n  closeOnOverlayClick?: boolean\n  closeOnEscape?: boolean\n  showCloseButton?: boolean\n  className?: string\n}\n\nconst sizeClasses = {\n  sm: 'max-w-md',\n  md: 'max-w-lg',\n  lg: 'max-w-2xl',\n  xl: 'max-w-4xl',\n  full: 'max-w-full mx-4',\n}\n\n/**\n * Accessible modal component with focus management and ARIA support\n */\nexport function Modal({\n  isOpen,\n  onClose,\n  title,\n  description,\n  children,\n  size = 'md',\n  closeOnOverlayClick = true,\n  closeOnEscape = true,\n  showCloseButton = true,\n  className,\n}: ModalProps) {\n  const titleId = useId()\n  const descriptionId = useId()\n  const containerRef = useFocusManagement(isOpen)\n\n  // Handle escape key\n  useEffect(() => {\n    if (!isOpen || !closeOnEscape) return\n\n    const handleEscape = (event: KeyboardEvent) => {\n      if (event.key === 'Escape') {\n        onClose()\n      }\n    }\n\n    document.addEventListener('keydown', handleEscape)\n    return () => document.removeEventListener('keydown', handleEscape)\n  }, [isOpen, closeOnEscape, onClose])\n\n  // Prevent body scroll when modal is open\n  useEffect(() => {\n    if (isOpen) {\n      document.body.style.overflow = 'hidden'\n    } else {\n      document.body.style.overflow = 'unset'\n    }\n\n    return () => {\n      document.body.style.overflow = 'unset'\n    }\n  }, [isOpen])\n\n  const handleOverlayClick = (event: React.MouseEvent) => {\n    if (closeOnOverlayClick && event.target === event.currentTarget) {\n      onClose()\n    }\n  }\n\n  return (\n    <AnimatePresence>\n      {isOpen && (\n        <div className=\"fixed inset-0 z-50 overflow-y-auto\">\n          {/* Backdrop */}\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"fixed inset-0 bg-black/50 backdrop-blur-sm\"\n            onClick={handleOverlayClick}\n          />\n\n          {/* Modal Container */}\n          <div className=\"flex min-h-full items-center justify-center p-4\">\n            <motion.div\n              ref={containerRef}\n              initial={{ opacity: 0, scale: 0.95, y: 20 }}\n              animate={{ opacity: 1, scale: 1, y: 0 }}\n              exit={{ opacity: 0, scale: 0.95, y: 20 }}\n              transition={{ duration: 0.2 }}\n              role=\"dialog\"\n              aria-modal=\"true\"\n              aria-labelledby={titleId}\n              aria-describedby={description ? descriptionId : undefined}\n              tabIndex={-1}\n              className={cn(\n                'relative w-full bg-white rounded-2xl shadow-xl',\n                'focus:outline-none',\n                sizeClasses[size],\n                className\n              )}\n            >\n              {/* Header */}\n              <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\n                <div>\n                  <h2\n                    id={titleId}\n                    className=\"text-xl font-poppins font-bold text-gray-900\"\n                  >\n                    {title}\n                  </h2>\n                  {description && (\n                    <p\n                      id={descriptionId}\n                      className=\"mt-1 text-sm text-gray-600\"\n                    >\n                      {description}\n                    </p>\n                  )}\n                </div>\n\n                {showCloseButton && (\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    onClick={onClose}\n                    className=\"p-2 -mr-2\"\n                    aria-label=\"Fechar modal\"\n                  >\n                    <XMarkIcon className=\"w-5 h-5\" />\n                  </Button>\n                )}\n              </div>\n\n              {/* Content */}\n              <div className=\"p-6\">\n                {children}\n              </div>\n            </motion.div>\n          </div>\n        </div>\n      )}\n    </AnimatePresence>\n  )\n}\n\n/**\n * Modal footer component for consistent button layout\n */\nexport function ModalFooter({\n  children,\n  className,\n}: {\n  children: React.ReactNode\n  className?: string\n}) {\n  return (\n    <div className={cn(\n      'flex items-center justify-end space-x-3 px-6 py-4 border-t border-gray-200 bg-gray-50 rounded-b-2xl',\n      className\n    )}>\n      {children}\n    </div>\n  )\n}\n\n/**\n * Confirmation modal for destructive actions\n */\nexport function ConfirmationModal({\n  isOpen,\n  onClose,\n  onConfirm,\n  title,\n  message,\n  confirmText = 'Confirmar',\n  cancelText = 'Cancelar',\n  variant = 'danger',\n}: {\n  isOpen: boolean\n  onClose: () => void\n  onConfirm: () => void\n  title: string\n  message: string\n  confirmText?: string\n  cancelText?: string\n  variant?: 'danger' | 'warning' | 'info'\n}) {\n  const variantStyles = {\n    danger: 'bg-red-600 hover:bg-red-700 text-white',\n    warning: 'bg-yellow-600 hover:bg-yellow-700 text-white',\n    info: 'bg-blue-600 hover:bg-blue-700 text-white',\n  }\n\n  return (\n    <Modal\n      isOpen={isOpen}\n      onClose={onClose}\n      title={title}\n      size=\"sm\"\n    >\n      <div className=\"mb-6\">\n        <p className=\"text-gray-700\">{message}</p>\n      </div>\n\n      <ModalFooter>\n        <Button\n          variant=\"outline\"\n          onClick={onClose}\n        >\n          {cancelText}\n        </Button>\n        <Button\n          onClick={() => {\n            onConfirm()\n            onClose()\n          }}\n          className={variantStyles[variant]}\n        >\n          {confirmText}\n        </Button>\n      </ModalFooter>\n    </Modal>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AAsBA,MAAM,cAAc;IAClB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,MAAM;AACR;AAKO,SAAS,MAAM,EACpB,MAAM,EACN,OAAO,EACP,KAAK,EACL,WAAW,EACX,QAAQ,EACR,OAAO,IAAI,EACX,sBAAsB,IAAI,EAC1B,gBAAgB,IAAI,EACpB,kBAAkB,IAAI,EACtB,SAAS,EACE;;IACX,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,QAAK,AAAD;IACpB,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,QAAK,AAAD;IAC1B,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,qBAAkB,AAAD,EAAE;IAExC,oBAAoB;IACpB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2BAAE;YACR,IAAI,CAAC,UAAU,CAAC,eAAe;YAE/B,MAAM;gDAAe,CAAC;oBACpB,IAAI,MAAM,GAAG,KAAK,UAAU;wBAC1B;oBACF;gBACF;;YAEA,SAAS,gBAAgB,CAAC,WAAW;YACrC;mCAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;;QACvD;0BAAG;QAAC;QAAQ;QAAe;KAAQ;IAEnC,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2BAAE;YACR,IAAI,QAAQ;gBACV,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC,OAAO;gBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC;YAEA;mCAAO;oBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBACjC;;QACF;0BAAG;QAAC;KAAO;IAEX,MAAM,qBAAqB,CAAC;QAC1B,IAAI,uBAAuB,MAAM,MAAM,KAAK,MAAM,aAAa,EAAE;YAC/D;QACF;IACF;IAEA,qBACE,6LAAC,4LAAA,CAAA,kBAAe;kBACb,wBACC,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;oBACV,SAAS;;;;;;8BAIX,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,KAAK;wBACL,SAAS;4BAAE,SAAS;4BAAG,OAAO;4BAAM,GAAG;wBAAG;wBAC1C,SAAS;4BAAE,SAAS;4BAAG,OAAO;4BAAG,GAAG;wBAAE;wBACtC,MAAM;4BAAE,SAAS;4BAAG,OAAO;4BAAM,GAAG;wBAAG;wBACvC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,MAAK;wBACL,cAAW;wBACX,mBAAiB;wBACjB,oBAAkB,cAAc,gBAAgB;wBAChD,UAAU,CAAC;wBACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kDACA,sBACA,WAAW,CAAC,KAAK,EACjB;;0CAIF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDACC,IAAI;gDACJ,WAAU;0DAET;;;;;;4CAEF,6BACC,6LAAC;gDACC,IAAI;gDACJ,WAAU;0DAET;;;;;;;;;;;;oCAKN,iCACC,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;wCACV,cAAW;kDAEX,cAAA,6LAAC,oNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAM3B,6LAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjB;GA5HgB;;QAYE,6JAAA,CAAA,QAAK;QACC,6JAAA,CAAA,QAAK;QACN,qIAAA,CAAA,qBAAkB;;;KAdzB;AAiIT,SAAS,YAAY,EAC1B,QAAQ,EACR,SAAS,EAIV;IACC,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,uGACA;kBAEC;;;;;;AAGP;MAfgB;AAoBT,SAAS,kBAAkB,EAChC,MAAM,EACN,OAAO,EACP,SAAS,EACT,KAAK,EACL,OAAO,EACP,cAAc,WAAW,EACzB,aAAa,UAAU,EACvB,UAAU,QAAQ,EAUnB;IACC,MAAM,gBAAgB;QACpB,QAAQ;QACR,SAAS;QACT,MAAM;IACR;IAEA,qBACE,6LAAC;QACC,QAAQ;QACR,SAAS;QACT,OAAO;QACP,MAAK;;0BAEL,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;8BAAiB;;;;;;;;;;;0BAGhC,6LAAC;;kCACC,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,SAAS;kCAER;;;;;;kCAEH,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS;4BACP;4BACA;wBACF;wBACA,WAAW,aAAa,CAAC,QAAQ;kCAEhC;;;;;;;;;;;;;;;;;;AAKX;MAvDgB", "debugId": null}}, {"offset": {"line": 3292, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspace/novo/src/components/ui/index.ts"], "sourcesContent": ["// UI component exports\nexport { <PERSON><PERSON> } from './Button'\nexport { Card, CardHeader, CardTitle, CardContent, CardFooter } from './Card'\nexport { Skeleton, SkeletonCard } from './Skeleton'\nexport { ErrorBoundary, useErrorHandler } from './ErrorBoundary'\nexport { LazyLoad, withLazyLoading, LazySection } from './LazyLoad'\nexport { ThemeToggle } from './ThemeToggle'\nexport { SkipLink, SkipNavigation } from './SkipLink'\nexport { Announcer, useAnnouncer } from './Announcer'\nexport { Tooltip, SimpleTooltip } from './Tooltip'\nexport { Modal, ModalFooter, ConfirmationModal } from './Modal'\n"], "names": [], "mappings": "AAAA,uBAAuB;;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 3341, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspace/novo/src/components/layout/Container.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface ContainerProps extends React.HTMLAttributes<HTMLDivElement> {\n  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'\n  children: React.ReactNode\n}\n\nconst containerSizes = {\n  sm: 'max-w-3xl',\n  md: 'max-w-5xl',\n  lg: 'max-w-6xl',\n  xl: 'max-w-7xl',\n  full: 'max-w-full',\n}\n\nexport function Container({\n  size = 'xl',\n  className,\n  children,\n  ...props\n}: ContainerProps) {\n  return (\n    <div\n      className={cn(\n        'mx-auto px-4 sm:px-6 lg:px-8',\n        containerSizes[size],\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAOA,MAAM,iBAAiB;IACrB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,MAAM;AACR;AAEO,SAAS,UAAU,EACxB,OAAO,IAAI,EACX,SAAS,EACT,QAAQ,EACR,GAAG,OACY;IACf,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gCACA,cAAc,CAAC,KAAK,EACpB;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;KAlBgB", "debugId": null}}, {"offset": {"line": 3378, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspace/novo/src/components/layout/Footer.tsx"], "sourcesContent": ["'use client'\n\nimport { But<PERSON> } from '@/components/ui'\nimport { CONTACT_INFO, SOCIAL_LINKS } from '@/constants'\nimport { scrollToElement } from '@/lib/utils'\nimport { ArrowUpIcon, EnvelopeIcon, MapPinIcon, PhoneIcon } from '@heroicons/react/24/outline'\nimport { motion } from 'framer-motion'\nimport { Container } from './Container'\n\nconst footerLinks = {\n  platform: {\n    title: 'Plataforma',\n    links: [\n      { name: 'Como Funciona', href: '#how-it-works' },\n      { name: 'Comparar <PERSON>', href: '#rates' },\n      { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', href: '#stores' },\n      { name: 'Todas as Plataformas', href: '#platforms' },\n      { name: 'Ofertas Especiais', href: '#offers' },\n    ],\n  },\n  resources: {\n    title: 'Recursos',\n    links: [\n      { name: 'Blog', href: '/blog' },\n      { name: '<PERSON><PERSON><PERSON>', href: '/guide' },\n      { name: '<PERSON>ertas de Taxa', href: '/alerts' },\n      { name: 'Documentação da API', href: '/api' },\n      { name: 'Central de Ajuda', href: '/help' },\n    ],\n  },\n  company: {\n    title: 'Empresa',\n    links: [\n      { name: 'Sobre Nós', href: '/about' },\n      { name: 'Carreiras', href: '/careers' },\n      { name: 'Imprensa', href: '/press' },\n      { name: 'Parcerias', href: '/partnerships' },\n      { name: 'Contato', href: '/contact' },\n    ],\n  },\n  legal: {\n    title: 'Legal',\n    links: [\n      { name: 'Política de Privacidade', href: '/privacy' },\n      { name: 'Termos de Uso', href: '/terms' },\n      { name: 'Política de Cookies', href: '/cookies' },\n      { name: 'LGPD', href: '/lgpd' },\n      { name: 'Disclaimer', href: '/disclaimer' },\n    ],\n  },\n} as const\n\nconst socialIcons = {\n  twitter: (\n    <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n      <path d=\"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84\" />\n    </svg>\n  ),\n  instagram: (\n    <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n      <path\n        fillRule=\"evenodd\"\n        d=\"M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987s11.987-5.367 11.987-11.987C24.004 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.229 14.794 3.74 13.643 3.74 12.346s.49-2.448 1.386-3.323c.896-.875 2.026-1.297 3.323-1.297s2.448.422 3.323 1.297c.875.875 1.297 2.026 1.297 3.323s-.422 2.448-1.297 3.323c-.875.875-2.026 1.297-3.323 1.297z\"\n        clipRule=\"evenodd\"\n      />\n    </svg>\n  ),\n  linkedin: (\n    <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n      <path d=\"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\" />\n    </svg>\n  ),\n}\n\nexport function Footer() {\n  const scrollToTop = () => {\n    window.scrollTo({ top: 0, behavior: 'smooth' })\n  }\n\n  const handleLinkClick = (href: string) => {\n    if (href.startsWith('#')) {\n      scrollToElement(href.substring(1))\n    }\n  }\n\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <Container>\n        <div className=\"py-16\">\n          {/* Main Footer Content */}\n          <div className=\"grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-5 lg:gap-12\">\n            {/* Company Info */}\n            <div className=\"lg:col-span-2\">\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6 }}\n                viewport={{ once: true }}\n              >\n                {/* Logo */}\n                <div className=\"mb-6 flex items-center space-x-2\">\n                  <div className=\"shadow-green-medium flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-emerald-500 to-emerald-600\">\n                    <span className=\"text-xl font-bold text-white\">₵</span>\n                  </div>\n                  <span className=\"font-poppins text-2xl font-bold text-white\">CashBoost</span>\n                </div>\n\n                <p className=\"mb-6 leading-relaxed text-gray-300\">\n                  A plataforma mais completa para comparar taxas de cashback no Brasil. Maximize\n                  suas economias e nunca perca as melhores ofertas.\n                </p>\n\n                {/* Contact Info */}\n                <div className=\"space-y-3\">\n                  <div className=\"flex items-center space-x-3 text-gray-300\">\n                    <EnvelopeIcon className=\"h-5 w-5 text-emerald-400\" />\n                    <span>{CONTACT_INFO.email}</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3 text-gray-300\">\n                    <PhoneIcon className=\"h-5 w-5 text-emerald-400\" />\n                    <span>{CONTACT_INFO.phone}</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3 text-gray-300\">\n                    <MapPinIcon className=\"h-5 w-5 text-emerald-400\" />\n                    <span>{CONTACT_INFO.address}</span>\n                  </div>\n                </div>\n              </motion.div>\n            </div>\n\n            {/* Footer Links */}\n            {Object.entries(footerLinks).map(([key, section], index) => (\n              <motion.div\n                key={key}\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                viewport={{ once: true }}\n              >\n                <h3 className=\"mb-4 font-poppins font-bold text-white\">{section.title}</h3>\n                <ul className=\"space-y-3\">\n                  {section.links.map(link => (\n                    <li key={link.name}>\n                      <button\n                        onClick={() => handleLinkClick(link.href)}\n                        className=\"text-left text-gray-300 transition-colors duration-200 hover:text-emerald-400\"\n                      >\n                        {link.name}\n                      </button>\n                    </li>\n                  ))}\n                </ul>\n              </motion.div>\n            ))}\n          </div>\n\n          {/* Newsletter Signup */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.4 }}\n            viewport={{ once: true }}\n            className=\"mt-16 border-t border-gray-800 pt-8\"\n          >\n            <div className=\"mx-auto max-w-md text-center lg:flex lg:max-w-none lg:items-center lg:justify-between lg:text-left\">\n              <div className=\"lg:flex-1\">\n                <h3 className=\"mb-2 font-poppins text-xl font-bold text-white\">\n                  Receba as Melhores Ofertas\n                </h3>\n                <p className=\"text-gray-300\">\n                  Seja notificado sobre aumentos de taxa e ofertas exclusivas\n                </p>\n              </div>\n              <div className=\"mt-6 lg:ml-8 lg:mt-0\">\n                <div className=\"flex flex-col gap-3 sm:flex-row\">\n                  <input\n                    type=\"email\"\n                    placeholder=\"Seu melhor e-mail\"\n                    className=\"flex-1 rounded-lg border border-gray-700 bg-gray-800 px-4 py-3 text-white placeholder-gray-400 focus:border-emerald-500 focus:outline-none focus:ring-2 focus:ring-emerald-500\"\n                  />\n                  <Button className=\"whitespace-nowrap\">Inscrever-se</Button>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n\n        {/* Bottom Footer */}\n        <div className=\"border-t border-gray-800 py-8\">\n          <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between\">\n            <div className=\"flex flex-col space-y-4 sm:flex-row sm:items-center sm:space-x-6 sm:space-y-0\">\n              <p className=\"text-sm text-gray-400\">\n                © 2024 CashBoost. Todos os direitos reservados.\n              </p>\n\n              {/* Social Links */}\n              <div className=\"flex space-x-4\">\n                {Object.entries(socialIcons).map(([platform, icon]) => (\n                  <a\n                    key={platform}\n                    href={SOCIAL_LINKS[platform as keyof typeof SOCIAL_LINKS]}\n                    className=\"text-gray-400 transition-colors duration-200 hover:text-emerald-400\"\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    aria-label={`Seguir no ${platform}`}\n                  >\n                    {icon}\n                  </a>\n                ))}\n              </div>\n            </div>\n\n            {/* Back to Top */}\n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              onClick={scrollToTop}\n              className=\"mt-6 inline-flex items-center space-x-2 text-gray-400 transition-colors duration-200 hover:text-emerald-400 lg:mt-0\"\n            >\n              <span className=\"text-sm\">Voltar ao Topo</span>\n              <ArrowUpIcon className=\"h-4 w-4\" />\n            </motion.button>\n          </div>\n        </div>\n      </Container>\n    </footer>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAPA;;;;;;;;AASA,MAAM,cAAc;IAClB,UAAU;QACR,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAiB,MAAM;YAAgB;YAC/C;gBAAE,MAAM;gBAAkB,MAAM;YAAS;YACzC;gBAAE,MAAM;gBAAoB,MAAM;YAAU;YAC5C;gBAAE,MAAM;gBAAwB,MAAM;YAAa;YACnD;gBAAE,MAAM;gBAAqB,MAAM;YAAU;SAC9C;IACH;IACA,WAAW;QACT,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAoB,MAAM;YAAS;YAC3C;gBAAE,MAAM;gBAAmB,MAAM;YAAU;YAC3C;gBAAE,MAAM;gBAAuB,MAAM;YAAO;YAC5C;gBAAE,MAAM;gBAAoB,MAAM;YAAQ;SAC3C;IACH;IACA,SAAS;QACP,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAa,MAAM;YAAS;YACpC;gBAAE,MAAM;gBAAa,MAAM;YAAW;YACtC;gBAAE,MAAM;gBAAY,MAAM;YAAS;YACnC;gBAAE,MAAM;gBAAa,MAAM;YAAgB;YAC3C;gBAAE,MAAM;gBAAW,MAAM;YAAW;SACrC;IACH;IACA,OAAO;QACL,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAA2B,MAAM;YAAW;YACpD;gBAAE,MAAM;gBAAiB,MAAM;YAAS;YACxC;gBAAE,MAAM;gBAAuB,MAAM;YAAW;YAChD;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAc,MAAM;YAAc;SAC3C;IACH;AACF;AAEA,MAAM,cAAc;IAClB,uBACE,6LAAC;QAAI,WAAU;QAAU,MAAK;QAAe,SAAQ;kBACnD,cAAA,6LAAC;YAAK,GAAE;;;;;;;;;;;IAGZ,yBACE,6LAAC;QAAI,WAAU;QAAU,MAAK;QAAe,SAAQ;kBACnD,cAAA,6LAAC;YACC,UAAS;YACT,GAAE;YACF,UAAS;;;;;;;;;;;IAIf,wBACE,6LAAC;QAAI,WAAU;QAAU,MAAK;QAAe,SAAQ;kBACnD,cAAA,6LAAC;YAAK,GAAE;;;;;;;;;;;AAGd;AAEO,SAAS;IACd,MAAM,cAAc;QAClB,OAAO,QAAQ,CAAC;YAAE,KAAK;YAAG,UAAU;QAAS;IAC/C;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,KAAK,UAAU,CAAC,MAAM;YACxB,CAAA,GAAA,sHAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,SAAS,CAAC;QACjC;IACF;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC,4IAAA,CAAA,YAAS;;8BACR,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,UAAU;4CAAE,MAAM;wCAAK;;0DAGvB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,WAAU;sEAA+B;;;;;;;;;;;kEAEjD,6LAAC;wDAAK,WAAU;kEAA6C;;;;;;;;;;;;0DAG/D,6LAAC;gDAAE,WAAU;0DAAqC;;;;;;0DAMlD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,0NAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;0EACxB,6LAAC;0EAAM,4HAAA,CAAA,eAAY,CAAC,KAAK;;;;;;;;;;;;kEAE3B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,oNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;0EACrB,6LAAC;0EAAM,4HAAA,CAAA,eAAY,CAAC,KAAK;;;;;;;;;;;;kEAE3B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,sNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;0EACtB,6LAAC;0EAAM,4HAAA,CAAA,eAAY,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAOlC,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE,sBAChD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO,QAAQ;wCAAI;wCAChD,UAAU;4CAAE,MAAM;wCAAK;;0DAEvB,6LAAC;gDAAG,WAAU;0DAA0C,QAAQ,KAAK;;;;;;0DACrE,6LAAC;gDAAG,WAAU;0DACX,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAA,qBACjB,6LAAC;kEACC,cAAA,6LAAC;4DACC,SAAS,IAAM,gBAAgB,KAAK,IAAI;4DACxC,WAAU;sEAET,KAAK,IAAI;;;;;;uDALL,KAAK,IAAI;;;;;;;;;;;uCATjB;;;;;;;;;;;sCAwBX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAiD;;;;;;0DAG/D,6LAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAI/B,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,WAAU;;;;;;8DAEZ,6LAAC,qIAAA,CAAA,SAAM;oDAAC,WAAU;8DAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQhD,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;kDAKrC,6LAAC;wCAAI,WAAU;kDACZ,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC,UAAU,KAAK,iBAChD,6LAAC;gDAEC,MAAM,4HAAA,CAAA,eAAY,CAAC,SAAsC;gDACzD,WAAU;gDACV,QAAO;gDACP,KAAI;gDACJ,cAAY,CAAC,UAAU,EAAE,UAAU;0DAElC;+CAPI;;;;;;;;;;;;;;;;0CAcb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,SAAS;gCACT,WAAU;;kDAEV,6LAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,6LAAC,wNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOrC;KAzJgB", "debugId": null}}, {"offset": {"line": 3984, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspace/novo/src/components/layout/Header.tsx"], "sourcesContent": ["'use client'\n\nimport { Bars3Icon, MagnifyingGlassIcon, XMarkIcon } from '@heroicons/react/24/outline'\nimport { AnimatePresence, motion } from 'framer-motion'\nimport Link from 'next/link'\nimport { useEffect, useState } from 'react'\n\nconst navigation = [\n  { name: 'Taxas ao Vivo', href: '#rates' },\n  { name: 'Comparar Plata<PERSON>', href: '#platforms' },\n  { name: 'Como Funciona', href: '#how-it-works' },\n  { name: 'Todas as Lojas', href: '#stores' },\n]\n\nexport function Header() {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)\n  const [scrolled, setScrolled] = useState(false)\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setScrolled(window.scrollY > 20)\n    }\n\n    window.addEventListener('scroll', handleScroll)\n    return () => window.removeEventListener('scroll', handleScroll)\n  }, [])\n\n  const scrollToSection = (href: string) => {\n    if (href.startsWith('#')) {\n      const element = document.querySelector(href)\n      if (element) {\n        element.scrollIntoView({ behavior: 'smooth' })\n      }\n    }\n    setMobileMenuOpen(false)\n  }\n\n  return (\n    <header\n      className={`fixed left-0 right-0 top-0 z-50 transition-all duration-300 ${\n        scrolled ? 'glass shadow-lg backdrop-blur-md' : 'bg-transparent'\n      }`}\n    >\n      <nav className=\"container-responsive\" aria-label=\"Global\">\n        <div className=\"flex h-16 items-center justify-between lg:h-20\">\n          {/* Logo */}\n          <div className=\"flex lg:flex-1\">\n            <Link href=\"/\" className=\"focus-visible -m-1.5 p-1.5\">\n              <span className=\"sr-only\">CashBoost</span>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"shadow-green-medium flex h-8 w-8 items-center justify-center rounded-xl bg-gradient-to-br from-emerald-500 to-emerald-600 lg:h-10 lg:w-10\">\n                  <span className=\"text-lg font-bold text-white lg:text-xl\">₵</span>\n                </div>\n                <span className=\"gradient-text font-poppins text-xl font-bold lg:text-2xl\">\n                  CashBoost\n                </span>\n              </div>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden lg:flex lg:gap-x-8\">\n            {navigation.map(item => (\n              <button\n                key={item.name}\n                onClick={() => scrollToSection(item.href)}\n                className=\"focus-visible text-sm font-semibold leading-6 text-gray-700 transition-colors duration-200 hover:text-emerald-600\"\n              >\n                {item.name}\n              </button>\n            ))}\n          </div>\n\n          {/* Desktop CTA */}\n          <div className=\"hidden lg:flex lg:flex-1 lg:justify-end lg:gap-x-4\">\n            <button className=\"btn-secondary text-sm\">\n              <MagnifyingGlassIcon className=\"mr-2 h-4 w-4\" />\n              Buscar Lojas\n            </button>\n            <button className=\"btn-primary text-sm\">Comparar Taxas</button>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"flex lg:hidden\">\n            <button\n              type=\"button\"\n              className=\"focus-visible -m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-700 hover:text-emerald-600\"\n              onClick={() => setMobileMenuOpen(true)}\n            >\n              <span className=\"sr-only\">Open main menu</span>\n              <Bars3Icon className=\"h-6 w-6\" aria-hidden=\"true\" />\n            </button>\n          </div>\n        </div>\n      </nav>\n\n      {/* Mobile menu */}\n      <AnimatePresence>\n        {mobileMenuOpen && (\n          <>\n            {/* Backdrop */}\n            <motion.div\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              exit={{ opacity: 0 }}\n              className=\"fixed inset-0 z-40 bg-black/20 backdrop-blur-sm lg:hidden\"\n              onClick={() => setMobileMenuOpen(false)}\n            />\n\n            {/* Menu Panel */}\n            <motion.div\n              initial={{ x: '100%' }}\n              animate={{ x: 0 }}\n              exit={{ x: '100%' }}\n              transition={{ type: 'spring', damping: 25, stiffness: 200 }}\n              className=\"fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-white px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10 lg:hidden\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <a href=\"/\" className=\"-m-1.5 p-1.5\">\n                  <span className=\"sr-only\">CashBoost</span>\n                  <div className=\"flex items-center space-x-2\">\n                    <div className=\"shadow-green-medium flex h-8 w-8 items-center justify-center rounded-xl bg-gradient-to-br from-emerald-500 to-emerald-600\">\n                      <span className=\"text-lg font-bold text-white\">₵</span>\n                    </div>\n                    <span className=\"gradient-text font-poppins text-xl font-bold\">CashBoost</span>\n                  </div>\n                </a>\n                <button\n                  type=\"button\"\n                  className=\"focus-visible -m-2.5 rounded-md p-2.5 text-gray-700 hover:text-emerald-600\"\n                  onClick={() => setMobileMenuOpen(false)}\n                >\n                  <span className=\"sr-only\">Close menu</span>\n                  <XMarkIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n                </button>\n              </div>\n\n              <div className=\"mt-6 flow-root\">\n                <div className=\"-my-6 divide-y divide-gray-500/10\">\n                  <div className=\"space-y-2 py-6\">\n                    {navigation.map(item => (\n                      <button\n                        key={item.name}\n                        onClick={() => scrollToSection(item.href)}\n                        className=\"focus-visible -mx-3 block w-full rounded-lg px-3 py-2 text-left text-base font-semibold leading-7 text-gray-900 transition-colors duration-200 hover:bg-emerald-50 hover:text-emerald-600\"\n                      >\n                        {item.name}\n                      </button>\n                    ))}\n                  </div>\n\n                  <div className=\"space-y-4 py-6\">\n                    <button\n                      className=\"btn-secondary w-full justify-center\"\n                      onClick={() => setMobileMenuOpen(false)}\n                    >\n                      <MagnifyingGlassIcon className=\"mr-2 h-4 w-4\" />\n                      Buscar Lojas\n                    </button>\n                    <button\n                      className=\"btn-primary w-full justify-center\"\n                      onClick={() => setMobileMenuOpen(false)}\n                    >\n                      Comparar Taxas\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n          </>\n        )}\n      </AnimatePresence>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AACA;AAAA;AACA;AACA;;;AALA;;;;;AAOA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAiB,MAAM;IAAS;IACxC;QAAE,MAAM;QAAwB,MAAM;IAAa;IACnD;QAAE,MAAM;QAAiB,MAAM;IAAgB;IAC/C;QAAE,MAAM;QAAkB,MAAM;IAAU;CAC3C;AAEM,SAAS;;IACd,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;iDAAe;oBACnB,YAAY,OAAO,OAAO,GAAG;gBAC/B;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;oCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;2BAAG,EAAE;IAEL,MAAM,kBAAkB,CAAC;QACvB,IAAI,KAAK,UAAU,CAAC,MAAM;YACxB,MAAM,UAAU,SAAS,aAAa,CAAC;YACvC,IAAI,SAAS;gBACX,QAAQ,cAAc,CAAC;oBAAE,UAAU;gBAAS;YAC9C;QACF;QACA,kBAAkB;IACpB;IAEA,qBACE,6LAAC;QACC,WAAW,CAAC,4DAA4D,EACtE,WAAW,qCAAqC,kBAChD;;0BAEF,6LAAC;gBAAI,WAAU;gBAAuB,cAAW;0BAC/C,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,6LAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAA0C;;;;;;;;;;;0DAE5D,6LAAC;gDAAK,WAAU;0DAA2D;;;;;;;;;;;;;;;;;;;;;;;sCAQjF,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAA,qBACd,6LAAC;oCAEC,SAAS,IAAM,gBAAgB,KAAK,IAAI;oCACxC,WAAU;8CAET,KAAK,IAAI;mCAJL,KAAK,IAAI;;;;;;;;;;sCAUpB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAO,WAAU;;sDAChB,6LAAC,wOAAA,CAAA,sBAAmB;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGlD,6LAAC;oCAAO,WAAU;8CAAsB;;;;;;;;;;;;sCAI1C,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,kBAAkB;;kDAEjC,6LAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,6LAAC,oNAAA,CAAA,YAAS;wCAAC,WAAU;wCAAU,eAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOnD,6LAAC,4LAAA,CAAA,kBAAe;0BACb,gCACC;;sCAEE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS;4BAAE;4BACtB,MAAM;gCAAE,SAAS;4BAAE;4BACnB,WAAU;4BACV,SAAS,IAAM,kBAAkB;;;;;;sCAInC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,GAAG;4BAAO;4BACrB,SAAS;gCAAE,GAAG;4BAAE;4BAChB,MAAM;gCAAE,GAAG;4BAAO;4BAClB,YAAY;gCAAE,MAAM;gCAAU,SAAS;gCAAI,WAAW;4BAAI;4BAC1D,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,MAAK;4CAAI,WAAU;;8DACpB,6LAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAU;0EAA+B;;;;;;;;;;;sEAEjD,6LAAC;4DAAK,WAAU;sEAA+C;;;;;;;;;;;;;;;;;;sDAGnE,6LAAC;4CACC,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,kBAAkB;;8DAEjC,6LAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,6LAAC,oNAAA,CAAA,YAAS;oDAAC,WAAU;oDAAU,eAAY;;;;;;;;;;;;;;;;;;8CAI/C,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ,WAAW,GAAG,CAAC,CAAA,qBACd,6LAAC;wDAEC,SAAS,IAAM,gBAAgB,KAAK,IAAI;wDACxC,WAAU;kEAET,KAAK,IAAI;uDAJL,KAAK,IAAI;;;;;;;;;;0DASpB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,WAAU;wDACV,SAAS,IAAM,kBAAkB;;0EAEjC,6LAAC,wOAAA,CAAA,sBAAmB;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAGlD,6LAAC;wDACC,WAAU;wDACV,SAAS,IAAM,kBAAkB;kEAClC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYrB;GAhKgB;KAAA", "debugId": null}}, {"offset": {"line": 4433, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspace/novo/src/components/CashbackRateCard.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport {\n  ShieldCheckIcon,\n  ArrowTopRightOnSquareIcon\n} from '@heroicons/react/24/solid'\n\ninterface Platform {\n  name: string\n  rate: number\n  trend: 'up' | 'down' | 'stable'\n  trendPercent?: number\n}\n\ninterface Store {\n  id: string\n  name: string\n  logo: string\n  category: string\n  verified: boolean\n  trustScore: number\n}\n\ninterface CashbackRate {\n  id: string\n  store: Store\n  bestRate: number\n  platforms: Platform[]\n  featured: boolean\n  lastUpdated: string\n}\n\ninterface CashbackRateCardProps {\n  rate: CashbackRate\n  featured?: boolean\n  getTrendIcon: (trend: string, trendPercent?: number) => React.ReactNode\n}\n\nexport default function CashbackRateCard({ \n  rate, \n  featured = false, \n  getTrendIcon \n}: CashbackRateCardProps) {\n\n  return (\n    <motion.div\n      whileHover={{ y: -4, scale: 1.02 }}\n      transition={{ duration: 0.2 }}\n      className={`${\n        featured ? 'card-featured' : 'card'\n      } p-6 relative overflow-hidden group cursor-pointer`}\n    >\n      {/* Featured Badge */}\n      {featured && (\n        <div className=\"absolute top-4 right-4 bg-gradient-to-r from-yellow-400 to-orange-400 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg\">\n          FEATURED\n        </div>\n      )}\n\n      {/* Store Header */}\n      <div className=\"flex items-center space-x-3 mb-4\">\n        {/* Store Logo Placeholder */}\n        <div className=\"w-12 h-12 rounded-xl bg-gray-100 flex items-center justify-center shadow-sm\">\n          <span className=\"text-gray-600 font-bold text-lg\">\n            {rate.store.name.charAt(0)}\n          </span>\n        </div>\n\n        <div className=\"flex-1\">\n          <div className=\"flex items-center space-x-2 mb-1\">\n            <h3 className=\"font-poppins font-bold text-base text-gray-900\">\n              {rate.store.name}\n            </h3>\n            {rate.store.verified && (\n              <ShieldCheckIcon className=\"w-4 h-4 text-emerald-500\" />\n            )}\n          </div>\n\n          <p className=\"text-xs text-gray-500\">\n            {rate.store.category}\n          </p>\n        </div>\n      </div>\n\n      {/* Modern Rate Display */}\n      <div className=\"relative mb-4\">\n        <div className=\"bg-gradient-to-r from-emerald-500 via-emerald-400 to-green-400 rounded-2xl p-4 text-center relative overflow-hidden\">\n          {/* Background Pattern */}\n          <div className=\"absolute inset-0 opacity-10\">\n            <div className=\"absolute top-2 right-2 w-8 h-8 bg-white rounded-full\"></div>\n            <div className=\"absolute bottom-2 left-2 w-6 h-6 bg-white rounded-full\"></div>\n          </div>\n\n          <div className=\"relative z-10\">\n            <div className=\"text-3xl font-poppins font-black text-white mb-1\">\n              {rate.bestRate}%\n            </div>\n            <div className=\"text-emerald-100 text-sm font-medium\">\n              Melhor Taxa\n            </div>\n          </div>\n\n          {/* Shine Effect */}\n          <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 animate-shine\"></div>\n        </div>\n      </div>\n\n      {/* Platform Comparison */}\n      <div className=\"space-y-3 mb-4\">\n        <h4 className=\"text-xs font-semibold text-gray-600 mb-3\">\n          Principais Plataformas:\n        </h4>\n        {rate.platforms.slice(0, 2).map((platform, index) => (\n          <div key={platform.name} className=\"bg-gray-50 rounded-xl p-3 flex items-center justify-between\">\n            <div className=\"flex items-center space-x-2\">\n              <div className={`w-2 h-2 rounded-full ${index === 0 ? 'bg-emerald-500' : 'bg-gray-400'}`}></div>\n              <span className=\"text-sm font-medium text-gray-700\">\n                {platform.name}\n              </span>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <span className=\"font-bold text-emerald-600 text-sm\">\n                {platform.rate}%\n              </span>\n              {getTrendIcon(platform.trend, platform.trendPercent)}\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Last Updated */}\n      <div className=\"text-center text-xs text-gray-500 mb-4\">\n        <span>Atualizado {rate.lastUpdated} • {rate.platforms.length} plataformas</span>\n      </div>\n\n      {/* Action Button */}\n      <motion.button\n        whileHover={{ scale: 1.02 }}\n        whileTap={{ scale: 0.98 }}\n        className=\"w-full bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 text-white font-semibold py-3 px-4 rounded-xl transition-all duration-200 text-sm shadow-lg hover:shadow-xl\"\n      >\n        Ver Todas as Taxas\n        <ArrowTopRightOnSquareIcon className=\"w-4 h-4 ml-1 inline\" />\n      </motion.button>\n\n      {/* Hover Effect Overlay */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-emerald-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none rounded-2xl\" />\n    </motion.div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAuCe,SAAS,iBAAiB,EACvC,IAAI,EACJ,WAAW,KAAK,EAChB,YAAY,EACU;IAEtB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,YAAY;YAAE,GAAG,CAAC;YAAG,OAAO;QAAK;QACjC,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAW,GACT,WAAW,kBAAkB,OAC9B,kDAAkD,CAAC;;YAGnD,0BACC,6LAAC;gBAAI,WAAU;0BAAsI;;;;;;0BAMvJ,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;sCACb,KAAK,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC;;;;;;;;;;;kCAI5B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDACX,KAAK,KAAK,CAAC,IAAI;;;;;;oCAEjB,KAAK,KAAK,CAAC,QAAQ,kBAClB,6LAAC,8NAAA,CAAA,kBAAe;wCAAC,WAAU;;;;;;;;;;;;0CAI/B,6LAAC;gCAAE,WAAU;0CACV,KAAK,KAAK,CAAC,QAAQ;;;;;;;;;;;;;;;;;;0BAM1B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;;;;;;;sCAGjB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;wCACZ,KAAK,QAAQ;wCAAC;;;;;;;8CAEjB,6LAAC;oCAAI,WAAU;8CAAuC;;;;;;;;;;;;sCAMxD,6LAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;0BAKnB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;oBAGxD,KAAK,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,UAAU,sBACzC,6LAAC;4BAAwB,WAAU;;8CACjC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAW,CAAC,qBAAqB,EAAE,UAAU,IAAI,mBAAmB,eAAe;;;;;;sDACxF,6LAAC;4CAAK,WAAU;sDACb,SAAS,IAAI;;;;;;;;;;;;8CAGlB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;;gDACb,SAAS,IAAI;gDAAC;;;;;;;wCAEhB,aAAa,SAAS,KAAK,EAAE,SAAS,YAAY;;;;;;;;2BAX7C,SAAS,IAAI;;;;;;;;;;;0BAkB3B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;;wBAAK;wBAAY,KAAK,WAAW;wBAAC;wBAAI,KAAK,SAAS,CAAC,MAAM;wBAAC;;;;;;;;;;;;0BAI/D,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,YAAY;oBAAE,OAAO;gBAAK;gBAC1B,UAAU;oBAAE,OAAO;gBAAK;gBACxB,WAAU;;oBACX;kCAEC,6LAAC,kPAAA,CAAA,4BAAyB;wBAAC,WAAU;;;;;;;;;;;;0BAIvC,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;KA/GwB", "debugId": null}}, {"offset": {"line": 4742, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspace/novo/src/components/TopRatesSection.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { motion } from 'framer-motion'\nimport {\n  ArrowUpIcon,\n  ArrowDownIcon,\n  MinusIcon,\n  StarIcon,\n  ArrowTopRightOnSquareIcon,\n  FunnelIcon\n} from '@heroicons/react/24/outline'\nimport { StarIcon as StarIconSolid } from '@heroicons/react/24/solid'\nimport CashbackRateCard from './CashbackRateCard'\n\ninterface Platform {\n  name: string\n  rate: number\n  trend: 'up' | 'down' | 'stable'\n  trendPercent?: number\n}\n\ninterface Store {\n  id: string\n  name: string\n  logo: string\n  category: string\n  verified: boolean\n  trustScore: number\n}\n\ninterface CashbackRateData {\n  id: string\n  store: Store\n  bestRate: number\n  platforms: Platform[]\n  featured: boolean\n  lastUpdated: string\n}\n\n// Mock data - in real app this would come from API\nconst topRates: CashbackRateData[] = [\n  {\n    id: '1',\n    store: {\n      id: 'nike',\n      name: '<PERSON>',\n      logo: '/logos/nike.svg',\n      category: 'Moda & Vestuário',\n      verified: true,\n      trustScore: 4.8,\n    },\n    bestRate: 8.5,\n    platforms: [\n      { name: '<PERSON><PERSON><PERSON>', rate: 8.5, trend: 'up', trendPercent: 15 },\n      { name: '<PERSON><PERSON><PERSON>', rate: 7.0, trend: 'stable' },\n      { name: 'TopCashback', rate: 6.5, trend: 'down', trendPercent: 5 },\n    ],\n    featured: true,\n    lastUpdated: 'há 2 horas',\n  },\n  {\n    id: '2',\n    store: {\n      id: 'amazon',\n      name: 'Amazon',\n      logo: '/logos/amazon.svg',\n      category: 'Marketplace',\n      verified: true,\n      trustScore: 4.9,\n    },\n    bestRate: 5.5,\n    platforms: [\n      { name: 'Inter Shopping', rate: 5.5, trend: 'up', trendPercent: 10 },\n      { name: 'Rakuten', rate: 4.8, trend: 'stable' },\n      { name: 'Meliuz', rate: 4.2, trend: 'up', trendPercent: 8 },\n    ],\n    featured: true,\n    lastUpdated: 'há 1 hora',\n  },\n  {\n    id: '3',\n    store: {\n      id: 'target',\n      name: 'Target',\n      logo: '/logos/target.svg',\n      category: 'Loja de Departamento',\n      verified: true,\n      trustScore: 4.7,\n    },\n    bestRate: 4.2,\n    platforms: [\n      { name: 'Banco Pan', rate: 4.2, trend: 'up', trendPercent: 20 },\n      { name: 'Meliuz', rate: 3.8, trend: 'stable' },\n      { name: 'Inter Shopping', rate: 3.5, trend: 'down', trendPercent: 3 },\n    ],\n    featured: false,\n    lastUpdated: 'há 3 horas',\n  },\n  {\n    id: '4',\n    store: {\n      id: 'bestbuy',\n      name: 'Best Buy',\n      logo: '/logos/bestbuy.svg',\n      category: 'Eletrônicos',\n      verified: true,\n      trustScore: 4.6,\n    },\n    bestRate: 4.0,\n    platforms: [\n      { name: 'TopCashback', rate: 4.0, trend: 'stable' },\n      { name: 'Rakuten', rate: 3.5, trend: 'up', trendPercent: 12 },\n      { name: 'Honey', rate: 3.2, trend: 'stable' },\n    ],\n    featured: false,\n    lastUpdated: 'há 4 horas',\n  },\n  {\n    id: '5',\n    store: {\n      id: 'walmart',\n      name: 'Walmart',\n      logo: '/logos/walmart.svg',\n      category: 'Loja de Departamento',\n      verified: true,\n      trustScore: 4.5,\n    },\n    bestRate: 3.8,\n    platforms: [\n      { name: 'TopCashback', rate: 3.8, trend: 'up', trendPercent: 18 },\n      { name: 'BeFrugal', rate: 3.2, trend: 'stable' },\n      { name: 'Rakuten', rate: 2.8, trend: 'down', trendPercent: 7 },\n    ],\n    featured: false,\n    lastUpdated: 'há 2 horas',\n  },\n  {\n    id: '6',\n    store: {\n      id: 'macys',\n      name: \"Macy's\",\n      logo: '/logos/macys.svg',\n      category: 'Moda & Vestuário',\n      verified: true,\n      trustScore: 4.4,\n    },\n    bestRate: 6.2,\n    platforms: [\n      { name: 'Rakuten', rate: 6.2, trend: 'up', trendPercent: 25 },\n      { name: 'TopCashback', rate: 5.8, trend: 'stable' },\n      { name: 'BeFrugal', rate: 5.5, trend: 'up', trendPercent: 10 },\n    ],\n    featured: false,\n    lastUpdated: 'há 1 hora',\n  },\n]\n\nconst categories = ['Todas', 'Moda & Vestuário', 'Eletrônicos', 'Loja de Departamento', 'Marketplace']\n\nexport default function TopRatesSection() {\n  const [selectedCategory, setSelectedCategory] = useState('Todas')\n  const [sortBy, setSortBy] = useState('rate') // 'rate', 'name', 'updated'\n\n  const filteredRates = topRates\n    .filter(rate => selectedCategory === 'Todas' || rate.store.category === selectedCategory)\n    .sort((a, b) => {\n      switch (sortBy) {\n        case 'rate':\n          return b.bestRate - a.bestRate\n        case 'name':\n          return a.store.name.localeCompare(b.store.name)\n        case 'updated':\n          return new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime()\n        default:\n          return 0\n      }\n    })\n\n  const getTrendIcon = (trend: string, trendPercent?: number) => {\n    switch (trend) {\n      case 'up':\n        return <ArrowUpIcon className=\"w-4 h-4 text-emerald-500\" />\n      case 'down':\n        return <ArrowDownIcon className=\"w-4 h-4 text-red-500\" />\n      default:\n        return <MinusIcon className=\"w-4 h-4 text-gray-400\" />\n    }\n  }\n\n  return (\n    <section id=\"rates\" className=\"py-responsive bg-white\">\n      <div className=\"container-responsive\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"inline-flex items-center px-4 py-2 rounded-full bg-emerald-50 text-emerald-700 font-medium text-sm mb-6\"\n          >\n            <ArrowUpIcon className=\"w-4 h-4 mr-2\" />\n            Taxas de Cashback ao Vivo\n          </motion.div>\n\n          <motion.h2\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.1 }}\n            viewport={{ once: true }}\n            className=\"text-responsive-2xl font-poppins font-bold text-gray-900 mb-4\"\n          >\n            Comparação de Taxas{' '}\n            <span className=\"gradient-text\">ao Vivo</span>\n          </motion.h2>\n\n          <motion.p\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.2 }}\n            viewport={{ once: true }}\n            className=\"text-responsive-sm text-gray-600 max-w-2xl mx-auto\"\n          >\n            Compare taxas de cashback instantaneamente entre plataformas brasileiras e internacionais.\n            Veja qual plataforma oferece a melhor taxa para cada loja - sem necessidade de cadastro.\n          </motion.p>\n        </div>\n\n        {/* Filters */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.3 }}\n          viewport={{ once: true }}\n          className=\"flex flex-col lg:flex-row justify-between items-start lg:items-center gap-6 mb-12\"\n        >\n          {/* Category Filters */}\n          <div className=\"flex flex-wrap gap-2\">\n            {categories.map((category) => (\n              <button\n                key={category}\n                onClick={() => setSelectedCategory(category)}\n                className={`px-4 py-2 rounded-xl font-medium text-sm transition-all duration-200 ${\n                  selectedCategory === category\n                    ? 'bg-emerald-500 text-white shadow-green-soft'\n                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'\n                }`}\n              >\n                {category}\n              </button>\n            ))}\n          </div>\n\n          {/* Sort Options */}\n          <div className=\"flex items-center gap-4\">\n            <FunnelIcon className=\"w-5 h-5 text-gray-400\" />\n            <select\n              value={sortBy}\n              onChange={(e) => setSortBy(e.target.value)}\n              className=\"px-4 py-2 rounded-xl border border-gray-200 text-sm font-medium text-gray-700 focus:border-emerald-500 focus:ring-2 focus:ring-emerald-100 outline-none\"\n            >\n              <option value=\"rate\">Maior Taxa</option>\n              <option value=\"name\">Nome da Loja</option>\n              <option value=\"updated\">Recém Atualizado</option>\n            </select>\n          </div>\n        </motion.div>\n\n        {/* Cashback Rate Cards Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {filteredRates.map((rate, index) => (\n            <motion.div\n              key={rate.id}\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n              viewport={{ once: true }}\n            >\n              <CashbackRateCard\n                rate={rate}\n                featured={rate.featured}\n                getTrendIcon={getTrendIcon}\n              />\n            </motion.div>\n          ))}\n        </div>\n\n        {/* View All Button */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.5 }}\n          viewport={{ once: true }}\n          className=\"text-center mt-12\"\n        >\n          <button className=\"btn-primary text-lg px-8 py-4\">\n            Comparar Todas as 500+ Lojas\n            <ArrowTopRightOnSquareIcon className=\"w-5 h-5 ml-2\" />\n          </button>\n        </motion.div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AASA;;;AAbA;;;;;AAwCA,mDAAmD;AACnD,MAAM,WAA+B;IACnC;QACE,IAAI;QACJ,OAAO;YACL,IAAI;YACJ,MAAM;YACN,MAAM;YACN,UAAU;YACV,UAAU;YACV,YAAY;QACd;QACA,UAAU;QACV,WAAW;YACT;gBAAE,MAAM;gBAAU,MAAM;gBAAK,OAAO;gBAAM,cAAc;YAAG;YAC3D;gBAAE,MAAM;gBAAW,MAAM;gBAAK,OAAO;YAAS;YAC9C;gBAAE,MAAM;gBAAe,MAAM;gBAAK,OAAO;gBAAQ,cAAc;YAAE;SAClE;QACD,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;YACL,IAAI;YAC<PERSON>,MAAM;YACN,MAAM;YACN,UAAU;YACV,UAAU;YACV,YAAY;QACd;QACA,UAAU;QACV,WAAW;YACT;gBAAE,MAAM;gBAAkB,MAAM;gBAAK,OAAO;gBAAM,cAAc;YAAG;YACnE;gBAAE,MAAM;gBAAW,MAAM;gBAAK,OAAO;YAAS;YAC9C;gBAAE,MAAM;gBAAU,MAAM;gBAAK,OAAO;gBAAM,cAAc;YAAE;SAC3D;QACD,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;YACL,IAAI;YACJ,MAAM;YACN,MAAM;YACN,UAAU;YACV,UAAU;YACV,YAAY;QACd;QACA,UAAU;QACV,WAAW;YACT;gBAAE,MAAM;gBAAa,MAAM;gBAAK,OAAO;gBAAM,cAAc;YAAG;YAC9D;gBAAE,MAAM;gBAAU,MAAM;gBAAK,OAAO;YAAS;YAC7C;gBAAE,MAAM;gBAAkB,MAAM;gBAAK,OAAO;gBAAQ,cAAc;YAAE;SACrE;QACD,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;YACL,IAAI;YACJ,MAAM;YACN,MAAM;YACN,UAAU;YACV,UAAU;YACV,YAAY;QACd;QACA,UAAU;QACV,WAAW;YACT;gBAAE,MAAM;gBAAe,MAAM;gBAAK,OAAO;YAAS;YAClD;gBAAE,MAAM;gBAAW,MAAM;gBAAK,OAAO;gBAAM,cAAc;YAAG;YAC5D;gBAAE,MAAM;gBAAS,MAAM;gBAAK,OAAO;YAAS;SAC7C;QACD,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;YACL,IAAI;YACJ,MAAM;YACN,MAAM;YACN,UAAU;YACV,UAAU;YACV,YAAY;QACd;QACA,UAAU;QACV,WAAW;YACT;gBAAE,MAAM;gBAAe,MAAM;gBAAK,OAAO;gBAAM,cAAc;YAAG;YAChE;gBAAE,MAAM;gBAAY,MAAM;gBAAK,OAAO;YAAS;YAC/C;gBAAE,MAAM;gBAAW,MAAM;gBAAK,OAAO;gBAAQ,cAAc;YAAE;SAC9D;QACD,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;YACL,IAAI;YACJ,MAAM;YACN,MAAM;YACN,UAAU;YACV,UAAU;YACV,YAAY;QACd;QACA,UAAU;QACV,WAAW;YACT;gBAAE,MAAM;gBAAW,MAAM;gBAAK,OAAO;gBAAM,cAAc;YAAG;YAC5D;gBAAE,MAAM;gBAAe,MAAM;gBAAK,OAAO;YAAS;YAClD;gBAAE,MAAM;gBAAY,MAAM;gBAAK,OAAO;gBAAM,cAAc;YAAG;SAC9D;QACD,UAAU;QACV,aAAa;IACf;CACD;AAED,MAAM,aAAa;IAAC;IAAS;IAAoB;IAAe;IAAwB;CAAc;AAEvF,SAAS;;IACtB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,4BAA4B;;IAEzE,MAAM,gBAAgB,SACnB,MAAM,CAAC,CAAA,OAAQ,qBAAqB,WAAW,KAAK,KAAK,CAAC,QAAQ,KAAK,kBACvE,IAAI,CAAC,CAAC,GAAG;QACR,OAAQ;YACN,KAAK;gBACH,OAAO,EAAE,QAAQ,GAAG,EAAE,QAAQ;YAChC,KAAK;gBACH,OAAO,EAAE,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,KAAK,CAAC,IAAI;YAChD,KAAK;gBACH,OAAO,IAAI,KAAK,EAAE,WAAW,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,WAAW,EAAE,OAAO;YAC5E;gBACE,OAAO;QACX;IACF;IAEF,MAAM,eAAe,CAAC,OAAe;QACnC,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,wNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,4NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC;gBACE,qBAAO,6LAAC,oNAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;QAChC;IACF;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAQ,WAAU;kBAC5B,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,6LAAC,wNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAI1C,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;gCACX;gCACqB;8CACpB,6LAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;sCAGlC,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;sCACX;;;;;;;;;;;;8BAOH,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAGV,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC;oCAEC,SAAS,IAAM,oBAAoB;oCACnC,WAAW,CAAC,qEAAqE,EAC/E,qBAAqB,WACjB,gDACA,+CACJ;8CAED;mCARI;;;;;;;;;;sCAcX,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,sNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;8CACtB,6LAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;oCACzC,WAAU;;sDAEV,6LAAC;4CAAO,OAAM;sDAAO;;;;;;sDACrB,6LAAC;4CAAO,OAAM;sDAAO;;;;;;sDACrB,6LAAC;4CAAO,OAAM;sDAAU;;;;;;;;;;;;;;;;;;;;;;;;8BAM9B,6LAAC;oBAAI,WAAU;8BACZ,cAAc,GAAG,CAAC,CAAC,MAAM,sBACxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;sCAEvB,cAAA,6LAAC,yIAAA,CAAA,UAAgB;gCACf,MAAM;gCACN,UAAU,KAAK,QAAQ;gCACvB,cAAc;;;;;;2BATX,KAAK,EAAE;;;;;;;;;;8BAgBlB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,6LAAC;wBAAO,WAAU;;4BAAgC;0CAEhD,6LAAC,oPAAA,CAAA,4BAAyB;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMjD;GAhJwB;KAAA", "debugId": null}}, {"offset": {"line": 5308, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspace/novo/src/components/TrustSection.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { \n  ShieldCheckIcon,\n  UsersIcon,\n  CurrencyDollarIcon,\n  StarIcon,\n  HeartIcon,\n  TrophyIcon\n} from '@heroicons/react/24/outline'\n\nconst trustStats = [\n  {\n    id: 1,\n    value: '2.1M+',\n    label: 'Usuários Satisfeitos',\n    description: 'Compradores confiam no CashBoost para suas necessidades de cashback',\n    icon: UsersIcon,\n    color: 'from-blue-500 to-cyan-500',\n  },\n  {\n    id: 2,\n    value: 'R$52M+',\n    label: 'Economia Total',\n    description: 'Cashback ganho pelos membros da nossa comunidade',\n    icon: CurrencyDollarIcon,\n    color: 'from-emerald-500 to-green-500',\n  },\n  {\n    id: 3,\n    value: '500+',\n    label: 'Lojas Parceiras',\n    description: 'Principais marcas e varejistas em nossa rede',\n    icon: ShieldCheckIcon,\n    color: 'from-purple-500 to-indigo-500',\n  },\n  {\n    id: 4,\n    value: '4.9/5',\n    label: 'Avaliação dos Usuários',\n    description: 'Avaliação média de usuários verificados',\n    icon: StarIcon,\n    color: 'from-yellow-500 to-orange-500',\n  },\n]\n\nconst testimonials = [\n  {\n    id: 1,\n    name: 'Ana Silva',\n    role: 'Compradora Online Frequente',\n    avatar: '/avatars/ana.jpg',\n    rating: 5,\n    text: \"O CashBoost mudou completamente como eu compro online. Economizei mais de R$800 este ano apenas comparando taxas antes de fazer compras. A interface é muito limpa e fácil de usar!\",\n    savings: 'R$847',\n    timeUsing: '8 meses',\n  },\n  {\n    id: 2,\n    name: 'Carlos Santos',\n    role: 'Entusiasta de Tecnologia',\n    avatar: '/avatars/carlos.jpg',\n    rating: 5,\n    text: \"Como alguém que compra muitos eletrônicos, encontrar as melhores taxas de cashback sempre foi um problema. O CashBoost torna isso sem esforço. Gostaria de ter encontrado esta plataforma antes!\",\n    savings: 'R$1.240',\n    timeUsing: '1 ano',\n  },\n  {\n    id: 3,\n    name: 'Mariana Costa',\n    role: 'Blogueira de Moda',\n    avatar: '/avatars/mariana.jpg',\n    rating: 5,\n    text: \"As atualizações de taxa em tempo real são incríveis. Peguei uma taxa de cashback de 12% na Nike que durou apenas algumas horas. Os alertas do CashBoost me salvaram de perder ofertas incríveis.\",\n    savings: 'R$623',\n    timeUsing: '6 meses',\n  },\n]\n\nconst features = [\n  {\n    title: 'Atualizações em Tempo Real',\n    description: 'Taxas de cashback atualizadas a cada hora',\n    icon: '⚡',\n  },\n  {\n    title: 'Sem Taxas Ocultas',\n    description: 'Completamente gratuito para usar, sempre',\n    icon: '💯',\n  },\n  {\n    title: 'Taxas Verificadas',\n    description: 'Todas as taxas verificadas e precisas',\n    icon: '✅',\n  },\n  {\n    title: 'Privacidade em Primeiro Lugar',\n    description: 'Seus dados permanecem privados e seguros',\n    icon: '🔒',\n  },\n]\n\nexport default function TrustSection() {\n  const renderStars = (rating: number) => {\n    return Array.from({ length: 5 }, (_, i) => (\n      <StarIcon\n        key={i}\n        className={`w-4 h-4 ${\n          i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'\n        }`}\n      />\n    ))\n  }\n\n  return (\n    <section className=\"py-responsive bg-white\">\n      <div className=\"container-responsive\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"inline-flex items-center px-4 py-2 rounded-full bg-emerald-50 text-emerald-700 font-medium text-sm mb-6\"\n          >\n            <HeartIcon className=\"w-4 h-4 mr-2\" />\n            Confiado por Milhões\n          </motion.div>\n\n          <motion.h2\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.1 }}\n            viewport={{ once: true }}\n            className=\"text-responsive-2xl font-poppins font-bold text-gray-900 mb-4\"\n          >\n            Junte-se à Comunidade de{' '}\n            <span className=\"gradient-text\">Compradores Inteligentes</span>\n          </motion.h2>\n\n          <motion.p\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.2 }}\n            viewport={{ once: true }}\n            className=\"text-responsive-sm text-gray-600 max-w-2xl mx-auto\"\n          >\n            Milhares de compradores já descobriram o poder da comparação inteligente de cashback.\n            Veja o que eles estão dizendo sobre o CashBoost.\n          </motion.p>\n        </div>\n\n        {/* Trust Stats */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16\">\n          {trustStats.map((stat, index) => (\n            <motion.div\n              key={stat.id}\n              initial={{ opacity: 0, y: 30, scale: 0.9 }}\n              whileInView={{ opacity: 1, y: 0, scale: 1 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n              viewport={{ once: true }}\n              className=\"text-center group\"\n            >\n              <motion.div\n                whileHover={{ scale: 1.05, rotate: 5 }}\n                transition={{ duration: 0.2 }}\n                className={`inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br ${stat.color} shadow-lg mb-4 group-hover:shadow-xl transition-shadow duration-300`}\n              >\n                <stat.icon className=\"w-8 h-8 text-white\" />\n              </motion.div>\n              \n              <motion.div\n                initial={{ scale: 0.8 }}\n                whileInView={{ scale: 1 }}\n                transition={{ duration: 0.8, delay: index * 0.1 + 0.3 }}\n                viewport={{ once: true }}\n                className=\"text-3xl lg:text-4xl font-poppins font-black text-gray-900 mb-2\"\n              >\n                {stat.value}\n              </motion.div>\n              \n              <h3 className=\"text-lg font-bold text-gray-900 mb-2\">\n                {stat.label}\n              </h3>\n              \n              <p className=\"text-sm text-gray-600 leading-relaxed\">\n                {stat.description}\n              </p>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Testimonials */}\n        <div className=\"mb-16\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"text-center mb-12\"\n          >\n            <h3 className=\"text-2xl font-poppins font-bold text-gray-900 mb-4\">\n              O que Nossos Usuários Dizem\n            </h3>\n            <p className=\"text-gray-600\">\n              Histórias reais de usuários reais que estão economizando mais com o CashBoost\n            </p>\n          </motion.div>\n\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n            {testimonials.map((testimonial, index) => (\n              <motion.div\n                key={testimonial.id}\n                initial={{ opacity: 0, y: 30 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.2 }}\n                viewport={{ once: true }}\n                className=\"bg-gradient-to-br from-gray-50 to-white rounded-3xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100\"\n              >\n                {/* User Info */}\n                <div className=\"flex items-center space-x-4 mb-6\">\n                  <div className=\"w-12 h-12 bg-gradient-to-br from-emerald-400 to-emerald-600 rounded-full flex items-center justify-center shadow-md\">\n                    <span className=\"text-white font-bold text-lg\">\n                      {testimonial.name.charAt(0)}\n                    </span>\n                  </div>\n                  <div>\n                    <h4 className=\"font-bold text-gray-900\">\n                      {testimonial.name}\n                    </h4>\n                    <p className=\"text-sm text-gray-600\">\n                      {testimonial.role}\n                    </p>\n                  </div>\n                </div>\n\n                {/* Rating */}\n                <div className=\"flex items-center space-x-1 mb-4\">\n                  {renderStars(testimonial.rating)}\n                </div>\n\n                {/* Testimonial Text */}\n                <blockquote className=\"text-gray-700 leading-relaxed mb-6 italic\">\n                  \"{testimonial.text}\"\n                </blockquote>\n\n                {/* Stats */}\n                <div className=\"flex justify-between items-center pt-4 border-t border-gray-200\">\n                  <div className=\"text-center\">\n                    <div className=\"text-lg font-bold text-emerald-600\">\n                      {testimonial.savings}\n                    </div>\n                    <div className=\"text-xs text-gray-500\">\n                      Total Economizado\n                    </div>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"text-lg font-bold text-gray-900\">\n                      {testimonial.timeUsing}\n                    </div>\n                    <div className=\"text-xs text-gray-500\">\n                      Usando CashBoost\n                    </div>\n                  </div>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n\n        {/* Features Grid */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          viewport={{ once: true }}\n          className=\"bg-gradient-to-br from-emerald-50 to-green-50 rounded-3xl p-8 lg:p-12\"\n        >\n          <div className=\"text-center mb-8\">\n            <TrophyIcon className=\"w-12 h-12 text-emerald-600 mx-auto mb-4\" />\n            <h3 className=\"text-2xl font-poppins font-bold text-gray-900 mb-4\">\n              Por que o CashBoost se Destaca\n            </h3>\n            <p className=\"text-gray-600\">\n              Estamos comprometidos em fornecer a melhor experiência de comparação de cashback\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            {features.map((feature, index) => (\n              <motion.div\n                key={feature.title}\n                initial={{ opacity: 0, scale: 0.9 }}\n                whileInView={{ opacity: 1, scale: 1 }}\n                transition={{ duration: 0.4, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                className=\"text-center p-6 bg-white/60 backdrop-blur-sm rounded-2xl hover:bg-white/80 transition-all duration-200\"\n              >\n                <div className=\"text-3xl mb-3\">\n                  {feature.icon}\n                </div>\n                <h4 className=\"font-bold text-gray-900 mb-2\">\n                  {feature.title}\n                </h4>\n                <p className=\"text-sm text-gray-600\">\n                  {feature.description}\n                </p>\n              </motion.div>\n            ))}\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAYA,MAAM,aAAa;IACjB;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,aAAa;QACb,MAAM,oNAAA,CAAA,YAAS;QACf,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,aAAa;QACb,MAAM,sOAAA,CAAA,qBAAkB;QACxB,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,aAAa;QACb,MAAM,gOAAA,CAAA,kBAAe;QACrB,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,aAAa;QACb,MAAM,kNAAA,CAAA,WAAQ;QACd,OAAO;IACT;CACD;AAED,MAAM,eAAe;IACnB;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,MAAM;QACN,SAAS;QACT,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,MAAM;QACN,SAAS;QACT,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,MAAM;QACN,SAAS;QACT,WAAW;IACb;CACD;AAED,MAAM,WAAW;IACf;QACE,OAAO;QACP,aAAa;QACb,MAAM;IACR;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM;IACR;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM;IACR;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM;IACR;CACD;AAEc,SAAS;IACtB,MAAM,cAAc,CAAC;QACnB,OAAO,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,CAAC,GAAG,kBACnC,6LAAC,kNAAA,CAAA,WAAQ;gBAEP,WAAW,CAAC,QAAQ,EAClB,IAAI,SAAS,iCAAiC,iBAC9C;eAHG;;;;;IAMX;IAEA,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,6LAAC,oNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAIxC,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;gCACX;gCAC0B;8CACzB,6LAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;sCAGlC,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;sCACX;;;;;;;;;;;;8BAOH,6LAAC;oBAAI,WAAU;8BACZ,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;gCAAI,OAAO;4BAAI;4BACzC,aAAa;gCAAE,SAAS;gCAAG,GAAG;gCAAG,OAAO;4BAAE;4BAC1C,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,YAAY;wCAAE,OAAO;wCAAM,QAAQ;oCAAE;oCACrC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,WAAW,CAAC,gFAAgF,EAAE,KAAK,KAAK,CAAC,oEAAoE,CAAC;8CAE9K,cAAA,6LAAC,KAAK,IAAI;wCAAC,WAAU;;;;;;;;;;;8CAGvB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,OAAO;oCAAI;oCACtB,aAAa;wCAAE,OAAO;oCAAE;oCACxB,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ,MAAM;oCAAI;oCACtD,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;8CAET,KAAK,KAAK;;;;;;8CAGb,6LAAC;oCAAG,WAAU;8CACX,KAAK,KAAK;;;;;;8CAGb,6LAAC;oCAAE,WAAU;8CACV,KAAK,WAAW;;;;;;;2BA9Bd,KAAK,EAAE;;;;;;;;;;8BAqClB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,6LAAC;oCAAG,WAAU;8CAAqD;;;;;;8CAGnE,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAK/B,6LAAC;4BAAI,WAAU;sCACZ,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAGV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEACb,YAAY,IAAI,CAAC,MAAM,CAAC;;;;;;;;;;;8DAG7B,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEACX,YAAY,IAAI;;;;;;sEAEnB,6LAAC;4DAAE,WAAU;sEACV,YAAY,IAAI;;;;;;;;;;;;;;;;;;sDAMvB,6LAAC;4CAAI,WAAU;sDACZ,YAAY,YAAY,MAAM;;;;;;sDAIjC,6LAAC;4CAAW,WAAU;;gDAA4C;gDAC9D,YAAY,IAAI;gDAAC;;;;;;;sDAIrB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACZ,YAAY,OAAO;;;;;;sEAEtB,6LAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;8DAIzC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACZ,YAAY,SAAS;;;;;;sEAExB,6LAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;mCAhDtC,YAAY,EAAE;;;;;;;;;;;;;;;;8BA2D3B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,sNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;8CACtB,6LAAC;oCAAG,WAAU;8CAAqD;;;;;;8CAGnE,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAK/B,6LAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,aAAa;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCACpC,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;sDACZ,QAAQ,IAAI;;;;;;sDAEf,6LAAC;4CAAG,WAAU;sDACX,QAAQ,KAAK;;;;;;sDAEhB,6LAAC;4CAAE,WAAU;sDACV,QAAQ,WAAW;;;;;;;mCAdjB,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBlC;KArNwB", "debugId": null}}]}