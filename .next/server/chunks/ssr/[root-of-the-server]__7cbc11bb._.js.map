{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_e5387405.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_e5387405-module__6kjfMG__className\",\n  \"variable\": \"inter_e5387405-module__6kjfMG__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_e5387405.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22variable%22:%22--font-inter%22,%22display%22:%22swap%22}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/poppins_a34304c2.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"poppins_a34304c2-module__4ARxxW__className\",\n  \"variable\": \"poppins_a34304c2-module__4ARxxW__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/poppins_a34304c2.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Poppins%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22weight%22:[%22300%22,%22400%22,%22500%22,%22600%22,%22700%22,%22800%22,%22900%22],%22variable%22:%22--font-poppins%22,%22display%22:%22swap%22}],%22variableName%22:%22poppins%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Poppins', 'Poppins Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,uJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,uJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,uJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspace/novo/src/app/layout.tsx"], "sourcesContent": ["import type { Metadata } from 'next'\nimport { Inter, Poppins } from 'next/font/google'\nimport './globals.css'\n\nconst inter = Inter({ \n  subsets: ['latin'],\n  variable: '--font-inter',\n  display: 'swap',\n})\n\nconst poppins = Poppins({ \n  subsets: ['latin'],\n  weight: ['300', '400', '500', '600', '700', '800', '900'],\n  variable: '--font-poppins',\n  display: 'swap',\n})\n\nexport const metadata: Metadata = {\n  title: 'CashBoost - Compare Taxas de Cashback e Maximize suas Economias',\n  description: 'Encontre as melhores taxas de cashback em todas as principais plataformas. Compare porcentagens de cashback em tempo real, descubra ofertas exclusivas e maximize suas economias com nossa ferramenta abrangente de comparação de cashback.',\n  keywords: 'comparação de cashback, taxas de cashback, compras online, economia, ofertas, plataformas de cashback, dinheiro de volta, recompensas de compras',\n  authors: [{ name: '<PERSON><PERSON><PERSON>' }],\n  creator: '<PERSON><PERSON><PERSON><PERSON>',\n  publisher: '<PERSON><PERSON><PERSON><PERSON>',\n  formatDetection: {\n    email: false,\n    address: false,\n    telephone: false,\n  },\n  metadataBase: new URL('https://cashboost.com.br'),\n  alternates: {\n    canonical: '/',\n  },\n  openGraph: {\n    title: 'CashBoost - Compare Taxas de Cashback e Maximize suas Economias',\n    description: 'Encontre as melhores taxas de cashback em todas as principais plataformas. Compare porcentagens de cashback em tempo real e maximize suas economias.',\n    url: 'https://cashboost.com.br',\n    siteName: 'CashBoost',\n    images: [\n      {\n        url: '/og-image.jpg',\n        width: 1200,\n        height: 630,\n        alt: 'CashBoost - Cashback Comparison Platform',\n      },\n    ],\n    locale: 'pt_BR',\n    type: 'website',\n  },\n  twitter: {\n    card: 'summary_large_image',\n    title: 'CashBoost - Compare Taxas de Cashback e Maximize suas Economias',\n    description: 'Encontre as melhores taxas de cashback em todas as principais plataformas. Compare porcentagens de cashback em tempo real e maximize suas economias.',\n    images: ['/og-image.jpg'],\n    creator: '@cashboost',\n  },\n  robots: {\n    index: true,\n    follow: true,\n    googleBot: {\n      index: true,\n      follow: true,\n      'max-video-preview': -1,\n      'max-image-preview': 'large',\n      'max-snippet': -1,\n    },\n  },\n  verification: {\n    google: 'your-google-verification-code',\n  },\n}\n\nexport default function RootLayout({\n  children,\n}: {\n  children: React.ReactNode\n}) {\n  return (\n    <html lang=\"pt-BR\" className={`${inter.variable} ${poppins.variable}`}>\n      <head>\n        <link rel=\"icon\" href=\"/favicon.ico\" />\n        <link rel=\"apple-touch-icon\" sizes=\"180x180\" href=\"/apple-touch-icon.png\" />\n        <link rel=\"icon\" type=\"image/png\" sizes=\"32x32\" href=\"/favicon-32x32.png\" />\n        <link rel=\"icon\" type=\"image/png\" sizes=\"16x16\" href=\"/favicon-16x16.png\" />\n        <link rel=\"manifest\" href=\"/manifest.json\" />\n        <meta name=\"theme-color\" content=\"#10B981\" />\n        <meta name=\"msapplication-TileColor\" content=\"#10B981\" />\n\n        {/* PWA Meta Tags */}\n        <meta name=\"application-name\" content=\"CashBoost\" />\n        <meta name=\"apple-mobile-web-app-capable\" content=\"yes\" />\n        <meta name=\"apple-mobile-web-app-status-bar-style\" content=\"default\" />\n        <meta name=\"apple-mobile-web-app-title\" content=\"CashBoost\" />\n        <meta name=\"mobile-web-app-capable\" content=\"yes\" />\n        <meta name=\"msapplication-config\" content=\"/browserconfig.xml\" />\n        <meta name=\"msapplication-tap-highlight\" content=\"no\" />\n\n        {/* Preload critical resources */}\n        <link rel=\"preconnect\" href=\"https://fonts.googleapis.com\" />\n        <link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" crossOrigin=\"anonymous\" />\n\n        {/* DNS Prefetch for external resources */}\n        <link rel=\"dns-prefetch\" href=\"//www.googletagmanager.com\" />\n        <link rel=\"dns-prefetch\" href=\"//fonts.googleapis.com\" />\n\n        {/* Service Worker Registration */}\n        <script\n          dangerouslySetInnerHTML={{\n            __html: `\n              if ('serviceWorker' in navigator) {\n                window.addEventListener('load', function() {\n                  navigator.serviceWorker.register('/sw.js')\n                    .then(function(registration) {\n                      console.log('SW registered: ', registration);\n                    })\n                    .catch(function(registrationError) {\n                      console.log('SW registration failed: ', registrationError);\n                    });\n                });\n              }\n            `,\n          }}\n        />\n      </head>\n      <body className=\"font-inter antialiased bg-green-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 overflow-x-hidden\">\n        {/* Skip Navigation Links */}\n        <a\n          href=\"#main-content\"\n          className=\"sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-emerald-600 focus:text-white focus:rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2\"\n        >\n          Pular para o conteúdo principal\n        </a>\n\n        <div className=\"min-h-screen\">\n          {children}\n        </div>\n\n        {/* Performance Dashboard (Development Only) */}\n        {process.env.NODE_ENV === 'development' && (\n          <div id=\"performance-dashboard\"></div>\n        )}\n      </body>\n    </html>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAiBO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;IACV,SAAS;QAAC;YAAE,MAAM;QAAmB;KAAE;IACvC,SAAS;IACT,WAAW;IACX,iBAAiB;QACf,OAAO;QACP,SAAS;QACT,WAAW;IACb;IACA,cAAc,IAAI,IAAI;IACtB,YAAY;QACV,WAAW;IACb;IACA,WAAW;QACT,OAAO;QACP,aAAa;QACb,KAAK;QACL,UAAU;QACV,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK;YACP;SACD;QACD,QAAQ;QACR,MAAM;IACR;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;QACb,QAAQ;YAAC;SAAgB;QACzB,SAAS;IACX;IACA,QAAQ;QACN,OAAO;QACP,QAAQ;QACR,WAAW;YACT,OAAO;YACP,QAAQ;YACR,qBAAqB,CAAC;YACtB,qBAAqB;YACrB,eAAe,CAAC;QAClB;IACF;IACA,cAAc;QACZ,QAAQ;IACV;AACF;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGT;IACC,qBACE,8OAAC;QAAK,MAAK;QAAQ,WAAW,GAAG,yIAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,2IAAA,CAAA,UAAO,CAAC,QAAQ,EAAE;;0BACnE,8OAAC;;kCACC,8OAAC;wBAAK,KAAI;wBAAO,MAAK;;;;;;kCACtB,8OAAC;wBAAK,KAAI;wBAAmB,OAAM;wBAAU,MAAK;;;;;;kCAClD,8OAAC;wBAAK,KAAI;wBAAO,MAAK;wBAAY,OAAM;wBAAQ,MAAK;;;;;;kCACrD,8OAAC;wBAAK,KAAI;wBAAO,MAAK;wBAAY,OAAM;wBAAQ,MAAK;;;;;;kCACrD,8OAAC;wBAAK,KAAI;wBAAW,MAAK;;;;;;kCAC1B,8OAAC;wBAAK,MAAK;wBAAc,SAAQ;;;;;;kCACjC,8OAAC;wBAAK,MAAK;wBAA0B,SAAQ;;;;;;kCAG7C,8OAAC;wBAAK,MAAK;wBAAmB,SAAQ;;;;;;kCACtC,8OAAC;wBAAK,MAAK;wBAA+B,SAAQ;;;;;;kCAClD,8OAAC;wBAAK,MAAK;wBAAwC,SAAQ;;;;;;kCAC3D,8OAAC;wBAAK,MAAK;wBAA6B,SAAQ;;;;;;kCAChD,8OAAC;wBAAK,MAAK;wBAAyB,SAAQ;;;;;;kCAC5C,8OAAC;wBAAK,MAAK;wBAAuB,SAAQ;;;;;;kCAC1C,8OAAC;wBAAK,MAAK;wBAA8B,SAAQ;;;;;;kCAGjD,8OAAC;wBAAK,KAAI;wBAAa,MAAK;;;;;;kCAC5B,8OAAC;wBAAK,KAAI;wBAAa,MAAK;wBAA4B,aAAY;;;;;;kCAGpE,8OAAC;wBAAK,KAAI;wBAAe,MAAK;;;;;;kCAC9B,8OAAC;wBAAK,KAAI;wBAAe,MAAK;;;;;;kCAG9B,8OAAC;wBACC,yBAAyB;4BACvB,QAAQ,CAAC;;;;;;;;;;;;YAYT,CAAC;wBACH;;;;;;;;;;;;0BAGJ,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBACC,MAAK;wBACL,WAAU;kCACX;;;;;;kCAID,8OAAC;wBAAI,WAAU;kCACZ;;;;;;oBAIF,oDAAyB,+BACxB,8OAAC;wBAAI,IAAG;;;;;;;;;;;;;;;;;;AAKlB", "debugId": null}}, {"offset": {"line": 369, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspace/novo/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}